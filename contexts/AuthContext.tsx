'use client';

import { createContext, ReactNode, useContext } from 'react';
import { UserResponse } from '@/types/user.types';
import { ApiResponseResource } from '@/types/base.types';

interface AuthContextType {
  authUser: ApiResponseResource<UserResponse>;
}

interface AuthProviderProps {
  children: ReactNode;
  authUser: ApiResponseResource<UserResponse>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);


export const AuthProvider = ({ children, authUser }: AuthProviderProps) => {
  return <AuthContext.Provider value={{ authUser }}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

import type { AvatarProps as AntdAvatarProps } from 'antd';
import { Avatar as AntdAvatar } from 'antd';
import { cn } from '@/lib/utils';

interface AvatarProps extends AntdAvatarProps {
  fallback?: string;
}

const KbAvatar = ({ className, src, fallback, ...props }: AvatarProps) => {
  return (
    <AntdAvatar src={src} className={cn('!rounded-full border shadow-sm', className)} {...props}>
      {!src && fallback}
    </AntdAvatar>
  );
};

export default KbAvatar;

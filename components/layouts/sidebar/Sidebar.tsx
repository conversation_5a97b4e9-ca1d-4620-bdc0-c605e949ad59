import SidebarItems from '@/components/layouts/sidebar/SidebarItems';

interface SidebarProps {
  isCollapsed: boolean;
}

const Sidebar = ({ isCollapsed }: SidebarProps) => {
  return (
    <div
      className={`${
        isCollapsed ? 'w-16' : 'w-64'
      } relative h-full border-r border-gray-200 bg-white shadow-lg transition-all duration-300 ease-in-out`}
    >
      <div className="absolute inset-0 bg-gradient-to-r from-blue-50/30 to-purple-50/30"></div>
      <div className="relative z-10 flex h-full flex-col">
        <div className={`${isCollapsed ? 'px-2' : 'px-4'} flex-shrink-0 py-4`}>
          <div className={`${isCollapsed ? 'text-center' : 'text-left'} mb-4`}>
            <h1
              className={`font-bold text-gray-800 ${isCollapsed ? 'text-xs' : 'text-[15px]'} transition-all duration-300`}
            >
              {isCollapsed ? 'NSM' : 'NSM COMPANY'}
            </h1>
            {!isCollapsed && (
              <p className="mt-1 text-[11px] font-medium text-gray-500">Seyahat Yönetim Sistemi</p>
            )}
          </div>
        </div>
        <div className="flex-1 overflow-hidden">
          <SidebarItems isCollapsed={isCollapsed} />
        </div>
      </div>
    </div>
  );
};

export default Sidebar;

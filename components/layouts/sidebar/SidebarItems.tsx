'use client';

import Link from 'next/link';
import { useState } from 'react';
import { menuGroups, MenuItem } from '@/components/layouts/sidebar/sidebarMenuLinks';
import { FaChevronDown, FaChevronRight } from 'react-icons/fa';
import { IoMdArrowDropright } from 'react-icons/io';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { enmUserOrganization } from '@/enums/UserOrganization';
import { useAuth } from '@/contexts/AuthContext';

interface NavItemProps {
  item: MenuItem;
  isCollapsed: boolean;
}

interface GroupTitleProps {
  title: string;
  isFirst: boolean;
  isCollapsed: boolean;
}

const NavItem = ({ item, isCollapsed }: NavItemProps) => {
  const { authUser } = useAuth();

  const userOrganization =
    authUser.status && authUser.data.organization_id === 1
      ? enmUserOrganization.AGENCY
      : enmUserOrganization.COMPANY;

  const { icon: Icon, href, label, isExpandable, subItems } = item;
  const [isOpen, setIsOpen] = useState(false);
  const ChevronIcon = isOpen ? FaChevronDown : FaChevronRight;

  const menuItem = (
    <div
      className={cn(
        'group mx-2 mb-1 flex items-center rounded-lg py-2.5 text-[13px] text-gray-700 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700',
        isCollapsed ? 'justify-center px-2' : 'justify-between px-3',
        item.organization && !item.organization.includes(userOrganization) && '!hidden',
      )}
    >
      <div
        className={cn(
          'flex cursor-pointer items-center',
          isCollapsed ? 'justify-center' : 'flex-1',
        )}
      >
        <div
          className={cn(
            'flex items-center justify-center rounded-lg transition-all duration-200',
            isCollapsed
              ? 'h-7 w-7 bg-gray-100 group-hover:bg-blue-100'
              : 'h-,7 mr-3 w-7 bg-gray-100 group-hover:bg-blue-100',
          )}
        >
          <Icon
            className={cn(
              'flex-shrink-0 text-gray-600 transition-colors duration-200 group-hover:text-blue-600',
              isCollapsed ? 'h-3.5 w-3.5' : 'h-3.5 w-3.5',
            )}
            aria-hidden="true"
          />
        </div>
        {!isCollapsed && (
          <span className="truncate font-medium transition-colors duration-200 group-hover:text-blue-700">
            {label}
          </span>
        )}
      </div>
      {!isCollapsed && isExpandable && (
        <button
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            setIsOpen(!isOpen);
          }}
          className="rounded-md p-1.5 transition-colors duration-200 hover:bg-gray-200"
          type="button"
        >
          <ChevronIcon className="h-3 w-3 text-gray-500 transition-colors duration-200 group-hover:text-blue-600" />
        </button>
      )}
    </div>
  );

  const subMenuItems = !isCollapsed && isOpen && subItems && (
    <div className="mr-2 mb-2 ml-6">
      {subItems.map((subItem) => (
        <Link
          key={subItem.href}
          href={subItem.href}
          className="group mb-1 flex items-center rounded-lg px-3 py-2 text-[13px] text-gray-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
        >
          <div className="mr-2.5 flex h-5 w-5 items-center justify-center rounded-md bg-gray-100 transition-all duration-200 group-hover:bg-blue-100">
            <IoMdArrowDropright className="h-2.5 w-2.5 text-gray-500 transition-colors duration-200 group-hover:text-blue-600" />
          </div>
          <span className="truncate font-medium transition-colors duration-200 group-hover:text-blue-700">
            {subItem.label}
          </span>
        </Link>
      ))}
    </div>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="block">
              <Link href={href}>{menuItem}</Link>
            </div>
          </TooltipTrigger>
          <TooltipContent side="right" className="border-gray-700 bg-gray-800 text-white shadow-xl">
            {label}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <div>
      {isExpandable ? (
        <div onClick={() => setIsOpen(!isOpen)}>{menuItem}</div>
      ) : (
        <Link href={href}>{menuItem}</Link>
      )}
      {subMenuItems}
    </div>
  );
};

const GroupTitle = ({ title, isFirst, isCollapsed }: GroupTitleProps) =>
  !isCollapsed && (
    <div
      className={`px-6 py-2 text-[11px] font-bold tracking-wider text-gray-500 uppercase ${isFirst ? 'mt-2' : 'mt-5'} mb-2`}
      role="heading"
      aria-level={2}
    >
      <span>{title}</span>
    </div>
  );

interface SidebarItemsProps {
  isCollapsed: boolean;
}

const SidebarItems = ({ isCollapsed }: SidebarItemsProps) => {
  return (
    <div className="flex h-full flex-col">
      <nav
        className="flex-1 overflow-y-auto [&::-webkit-scrollbar]:w-1 [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400 [&::-webkit-scrollbar-track]:bg-transparent"
        aria-label="Ana navigasyon"
      >
        {menuGroups.map((group, index) => (
          <div key={group.title} className="mb-4">
            <GroupTitle title={group.title} isFirst={index === 0} isCollapsed={isCollapsed} />
            <div className="space-y-1">
              {group.items.map((item) => (
                <NavItem key={item.href} item={item} isCollapsed={isCollapsed} />
              ))}
            </div>
          </div>
        ))}
      </nav>

      {!isCollapsed && (
        <div className="flex-shrink-0 border-t border-gray-200 p-3">
          <div className="mx-2 rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50 p-2.5">
            <div className="mb-1 text-[11px] font-medium text-gray-600">Version</div>
            <div className="flex items-center text-[11px] text-green-600">
              <div className="mr-1.5 h-1.5 w-1.5 animate-pulse rounded-full bg-green-500"></div>
              1.0.0
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SidebarItems;

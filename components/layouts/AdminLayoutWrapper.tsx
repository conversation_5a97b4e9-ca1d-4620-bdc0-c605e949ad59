'use client';

import { ReactNode, useEffect, useState } from 'react';
import Header from '@/components/layouts/header/Header';
import Sidebar from '@/components/layouts/sidebar/Sidebar';
import Loading from '@/app/loading';
import { UserResponse } from '@/types/user.types';
import { ApiResponseResource } from '@/types/base.types';
import NextTopLoader from 'nextjs-toploader';
import { AuthProvider } from '@/contexts/AuthContext';

interface AdminLayoutWrapperProps {
  children: ReactNode;
  initialCollapsed?: boolean;
  authUser: ApiResponseResource<UserResponse>;
}

const AdminLayoutWrapper = ({
  children,
  initialCollapsed = false,
  authUser,
}: AdminLayoutWrapperProps) => {
  const [isCollapsed, setIsCollapsed] = useState(initialCollapsed);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted) {
      localStorage.setItem('sidebarCollapsed', JSON.stringify(isCollapsed));
    }
  }, [isCollapsed, isMounted]);

  const toggleSidebar = () => {
    setIsCollapsed((prev) => !prev);
  };

  if (!isMounted) {
    return <Loading />;
  }

  return (
    <AuthProvider authUser={authUser}>
      <div className="flex h-screen flex-col">
        <NextTopLoader height={48} color="rgba(59,130,246,0.7)" />
        <Header onToggleSidebar={toggleSidebar} isCollapsed={isCollapsed} />
        <main className="flex flex-1 overflow-hidden">
          <Sidebar isCollapsed={isCollapsed} />
          <div className="flex flex-1 flex-col overflow-hidden">
            <div className="flex-1 overflow-auto bg-[#F5F5F5]">{children}</div>
          </div>
        </main>
      </div>
    </AuthProvider>
  );
};

export default AdminLayoutWrapper;

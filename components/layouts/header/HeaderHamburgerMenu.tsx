'use client';

import { Menu, X } from 'lucide-react';

interface HeaderHamburgerMenuProps {
  onToggleSidebar: () => void;
  isCollapsed: boolean;
}

const HeaderHamburgerMenu = ({ onToggleSidebar, isCollapsed }: HeaderHamburgerMenuProps) => {
  return (
    <button
      onClick={onToggleSidebar}
      className="flex h-8 w-8 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:bg-white/10 hover:shadow-lg active:scale-95"
      aria-label={isCollapsed ? "Sidebar'ı Aç" : "Sidebar'ı Kapat"}
    >
      {isCollapsed ? (
        <Menu className="h-5 w-5 text-white/70 transition-all duration-300 hover:rotate-180 hover:text-white" />
      ) : (
        <X className="h-5 w-5 text-white/70 transition-all duration-300 hover:rotate-90 hover:text-white" />
      )}
    </button>
  );
};

export default HeaderHamburgerMenu;

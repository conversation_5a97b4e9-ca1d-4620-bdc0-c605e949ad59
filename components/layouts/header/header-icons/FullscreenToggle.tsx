'use client';

import React, { useEffect, useState } from 'react';
import { BsFullscreen, BsFullscreenExit } from 'react-icons/bs';

const FullscreenToggle: React.FC = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  const handleToggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen().catch((err) => {
        console.error(`Tam ekran hatası: ${err.message}`);
      });
    } else {
      document.exitFullscreen().catch((err) => {
        console.error(`<PERSON> ekrandan çıkma hatası: ${err.message}`);
      });
    }
  };

  return (
    <div className="group relative">
      <button
        onClick={handleToggleFullscreen}
        className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110 hover:shadow-lg active:scale-95"
      >
        {isFullscreen ? (
          <BsFullscreenExit className="h-5 w-5 text-white/70 transition-all duration-300 group-hover:text-white group-hover:rotate-180" />
        ) : (
          <BsFullscreen className="h-5 w-5 text-white/70 transition-all duration-300 group-hover:text-white group-hover:scale-110" />
        )}
      </button>
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          {isFullscreen ? 'Tam ekrandan çık' : 'Tam ekran yap'}
        </div>
      </div>
    </div>
  );
};

export default FullscreenToggle;

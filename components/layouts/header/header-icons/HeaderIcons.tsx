import { BsBell, BsEnvelope, BsGear } from 'react-icons/bs';
import FullscreenToggle from '@/components/layouts/header/header-icons/FullscreenToggle';
import { useState } from 'react';

const HeaderIcons = () => {
  const [notifications] = useState(3); // Örnek bildirim sayısı
  const [messages] = useState(5); // Örnek mesaj sayısı

  return (
    <div className="flex items-center gap-3">
      {/* Settings Icon */}
      <div className="group relative">
        <button className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110 hover:shadow-lg active:scale-95">
          <BsGear className="h-5 w-5 text-white/70 transition-all duration-300 group-hover:text-white group-hover:rotate-90" />
        </button>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            Ayarlar
          </div>
        </div>
      </div>

      {/* Notifications Icon */}
      <div className="group relative">
        <button className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110 hover:shadow-lg active:scale-95">
          <div className="relative">
            <BsBell className="h-5 w-5 text-white/70 transition-all duration-300 group-hover:text-white group-hover:animate-pulse" />
            {notifications > 0 && (
              <span className="absolute -top-2 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-[10px] font-bold text-white animate-bounce">
                {notifications > 9 ? '9+' : notifications}
              </span>
            )}
          </div>
        </button>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            Bildirimler
          </div>
        </div>
      </div>

      {/* Messages Icon */}
      <div className="group relative">
        <button className="flex h-10 w-10 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:scale-110 hover:shadow-lg active:scale-95">
          <div className="relative">
            <BsEnvelope className="h-5 w-5 text-white/70 transition-all duration-300 group-hover:text-white group-hover:animate-pulse" />
            {messages > 0 && (
              <span className="absolute -top-2 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-blue-500 text-[10px] font-bold text-white animate-bounce">
                {messages > 9 ? '9+' : messages}
              </span>
            )}
          </div>
        </button>
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
          <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            Mesajlar
          </div>
        </div>
      </div>

      {/* Fullscreen Toggle */}
      <FullscreenToggle />
    </div>
  );
};

export default HeaderIcons;

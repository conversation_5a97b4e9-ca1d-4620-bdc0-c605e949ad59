'use client';

import { AiOutlineHome } from 'react-icons/ai';
import { FiChevronRight } from 'react-icons/fi';
import { ReactNode } from 'react';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';

interface BreadcrumbLink {
  title: string;
  link?: string;
}

interface HeaderBreadcrumbProps {
  content?: ReactNode | string;
  links?: BreadcrumbLink[];
  className?: string;
}

const BreadcrumbSeparator = () => (
  <FiChevronRight className="mx-2 flex-shrink-0 text-sm text-gray-400" aria-hidden="true" />
);

const HomeLink = () => (
  <Link
    href={ROUTES.HOME}
    className="group flex items-center gap-2 rounded-lg px-3 py-1.5 transition-all duration-200 hover:bg-blue-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:outline-none"
    aria-label="Ana sayfaya git"
  >
    <div className="flex h-7 w-7 items-center justify-center rounded-md bg-blue-50 transition-colors duration-200 group-hover:bg-blue-100">
      <AiOutlineHome className="text-sm text-blue-600" aria-hidden="true" />
    </div>
    <span className="text-sm font-medium text-gray-700 transition-colors duration-200 group-hover:text-blue-700">
      Anasayfa
    </span>
  </Link>
);

const BreadcrumbItem = ({ item }: { item: BreadcrumbLink }) => {
  const isLast = !item.link;

  if (isLast) {
    return (
      <span
        className="rounded-md bg-gray-50 px-2 py-1 text-sm font-medium text-gray-800"
        aria-current="page"
      >
        {item.title}
      </span>
    );
  }

  return (
    <Link
      className="group rounded-md px-2 py-1 transition-all duration-200 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:outline-none"
      href={item.link!}
      aria-label={`${item.title} sayfasına git`}
    >
      <span className="text-sm font-medium text-gray-600 transition-colors duration-200 group-hover:text-blue-600">
        {item.title}
      </span>
    </Link>
  );
};

const BreadcrumbNavigation = ({ links }: { links: BreadcrumbLink[] }) => (
  <nav aria-label="Breadcrumb" className="ml-2 flex items-center">
    {links.map((link, index) => (
      <div key={`${link.title}-${index}`} className="flex items-center">
        <BreadcrumbSeparator />
        <BreadcrumbItem item={link} />
      </div>
    ))}
  </nav>
);

const ContentSection = ({ content }: { content: ReactNode | string }) => {
  if (typeof content === 'string') {
    return (
      <div className="rounded-lg border border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50 px-3 py-1.5">
        <h1 className="text-sm font-semibold text-gray-700">{content}</h1>
      </div>
    );
  }

  return <>{content}</>;
};

const HeaderBreadcrumb = ({ content, links = [], className }: HeaderBreadcrumbProps) => {
  const hasLinks = links.length > 0;

  return (
    <header
      className={`mx-4 flex h-14 items-center justify-between rounded-b-2xl border-b border-b-gray-200 bg-white px-4 shadow-sm ${className || ''}`}
      role="banner"
    >
      <div className="flex min-w-0 flex-1 items-center">
        <HomeLink />
        {hasLinks && <BreadcrumbNavigation links={links} />}
      </div>

      {content && (
        <div className="ml-4 flex flex-shrink-0 items-center">
          <ContentSection content={content} />
        </div>
      )}
    </header>
  );
};

export default HeaderBreadcrumb;

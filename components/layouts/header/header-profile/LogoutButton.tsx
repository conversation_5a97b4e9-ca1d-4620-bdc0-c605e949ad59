'use client';

import { logout } from '@/app/services/auth.service';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useTransition } from 'react';
import { LogOut } from 'lucide-react';

const LogoutButton = () => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const handleLogout = async () => {
    startTransition(async () => {
      try {
        await logout();
        router.push(ROUTES.HOME);
      } catch (error) {
        console.error('Logout error:', error);
        router.push(ROUTES.HOME);
      }
    });
  };

  return (
    <button
      onClick={handleLogout}
      disabled={isPending}
      className="flex w-full items-center justify-center gap-2 px-3 py-2 text-sm text-red-600 rounded-md cursor-pointer hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
    >
      <LogOut className="h-4 w-4" />
      {isPending ? 'Çıkış...' : 'Çıkış Yap'}
    </button>
  );
};

export default LogoutButton;

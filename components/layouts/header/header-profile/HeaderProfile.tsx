import Link from 'next/link';
import KBAvatar from '@/components/ui/custom/KbAvatar';
import { ROUTES } from '@/lib/routes';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import LogoutButton from './LogoutButton';
import { User } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

const HeaderProfile = () => {
  const { authUser } = useAuth();

  if (!authUser.status) return null;

  const userInitials = `${authUser.data.first_name.charAt(0)}${authUser.data.last_name.charAt(0)}`;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="flex items-center gap-3 rounded-lg bg-white/10 px-3 py-2 transition-all duration-200 hover:ring-white/20 focus:ring-2 focus:outline-none">
          <KBAvatar
            src={`https://ui-avatars.com/api/?name=${authUser.data.first_name}+${authUser.data.last_name}&background=random`}
            fallback={userInitials}
            size={32}
            className="ring-2 ring-white/20"
          />
          <div className="flex flex-col text-left">
            <span className="text-sm font-medium text-white">
              {authUser.data.first_name} {authUser.data.last_name}
            </span>
            <span className="text-xs text-white/70">{authUser.data.email}</span>
          </div>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64 rounded-lg border shadow-lg" align="end" sideOffset={8}>
        <div className="flex flex-col items-center gap-3 border-b p-4">
          <KBAvatar
            src={`https://ui-avatars.com/api/?name=${authUser.data.first_name}+${authUser.data.last_name}&background=random`}
            fallback={userInitials}
            size={64}
            className="ring-2 ring-gray-200"
          />
          <div className="text-center">
            <div className="font-semibold text-gray-900">
              {authUser.data.first_name} {authUser.data.last_name}
            </div>
            <div className="text-sm text-gray-500">{authUser.data.email}</div>
          </div>
        </div>

        <div className="p-2">
          <div className="flex gap-2">
            <DropdownMenuItem asChild className="flex-1">
              <Link
                href={ROUTES.PROFILE.INDEX}
                className="flex cursor-pointer items-center justify-center gap-2 rounded-md px-3 py-2 text-sm"
              >
                <User className="h-4 w-4" />
                Profilim
              </Link>
            </DropdownMenuItem>

            <DropdownMenuItem asChild className="flex-1">
              <LogoutButton />
            </DropdownMenuItem>
          </div>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default HeaderProfile;

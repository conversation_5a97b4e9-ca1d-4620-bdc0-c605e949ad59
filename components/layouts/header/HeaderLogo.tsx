import { MdCardTravel } from 'react-icons/md';

interface HeaderLogoProps {
  isCollapsed: boolean;
}

const HeaderLogo = ({ isCollapsed }: HeaderLogoProps) => {
  return (
    <div
      className={`${isCollapsed ? 'w-14' : 'w-60'} inline-flex items-center gap-2 px-4 text-lg text-white transition-all duration-300`}
    >
      <MdCardTravel className="h-5 w-5 flex-shrink-0" />
      <div
        className={`transition-all duration-300 ${isCollapsed ? 'w-0 overflow-hidden opacity-0' : 'w-auto opacity-100'}`}
      >
        Bizi<span className="font-extrabold">Click</span>
      </div>
    </div>
  );
};

export default HeaderLogo;

import HeaderProfile from '@/components/layouts/header/header-profile/HeaderProfile';
import HeaderIcons from '@/components/layouts/header/header-icons/HeaderIcons';
import HeaderLogo from '@/components/layouts/header/HeaderLogo';
import HeaderHamburgerMenu from '@/components/layouts/header/HeaderHamburgerMenu';

interface HeaderProps {
  onToggleSidebar: () => void;
  isCollapsed: boolean;
}

const Header = ({ onToggleSidebar, isCollapsed }: HeaderProps) => {
  return (
    <div className="flex h-12 min-w-0 items-center bg-[#2C2C2C]">
      <HeaderLogo isCollapsed={isCollapsed} />
      <div className="flex flex-1 items-center justify-between px-4">
        <div className="flex items-center">
          <HeaderHamburgerMenu onToggleSidebar={onToggleSidebar} isCollapsed={isCollapsed} />
        </div>
        <div className="flex items-center gap-8">
          <HeaderIcons />
          <HeaderProfile />
        </div>
      </div>
    </div>
  );
};

export default Header;

'use client';

import { Select, SelectProps } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { ApiResponseCollection } from '@/types/base.types';

export interface SelectOption {
  value: string;
  label: string;
  searchValue: string;
}

export interface InfiniteSelectConfig<T> {
  mapToOption: (item: T) => SelectOption;
  getItemKey?: (item: T) => string;
  loadData: (nextPageUrl?: string) => Promise<ApiResponseCollection<T>>;
  searchData?: (searchTerm: string, nextPageUrl?: string) => Promise<ApiResponseCollection<T>>;
  searchDebounceMs?: number;
  placeholder: string;
  notFoundText: string;
  loadingText: string;
}

interface InfiniteSelectProps<T> extends Omit<SelectProps, 'options' | 'loading'> {
  config: InfiniteSelectConfig<T>;
  value?: string;
  onChange?: (value: string) => void;
}

const SCROLL_THRESHOLD = 15;
const SEARCH_DEBOUNCE_MS = 500;

function InfiniteSelect<T>({ config, value, onChange, ...props }: InfiniteSelectProps<T>) {
  const [items, setItems] = useState<T[]>([]);
  const [nextPageUrl, setNextPageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const loadItems = async (pageUrl: string = '', search: string = '') => {
    if (isLoading) return;

    setIsLoading(true);
    try {
      const response =
        search && config.searchData
          ? await config.searchData(search, pageUrl)
          : await config.loadData(pageUrl);

      if (response.status) {
        const newItems = response.data.items || [];

        setItems((prevItems) => (pageUrl === '' ? newItems : [...prevItems, ...newItems]));

        setNextPageUrl(response.data.meta.next_page || '');
      } else {
        // If search fails, clear items when it's a new search
        if (pageUrl === '') {
          setItems([]);
          setNextPageUrl('');
        }
      }
    } catch (error) {
      console.error('Failed to load items:', error);
      // Clear items on error for new searches
      if (pageUrl === '') {
        setItems([]);
        setNextPageUrl('');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDropdownOpen = () => {
    if (!isInitialized) {
      setIsInitialized(true);
      void loadItems('', searchTerm);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    if (scrollTimeoutRef.current) return;

    scrollTimeoutRef.current = setTimeout(() => {
      const { target } = e;
      const element = target as HTMLDivElement;

      if (!element || isLoading || !nextPageUrl) {
        scrollTimeoutRef.current = null;
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = element;
      const isNearBottom = scrollHeight - scrollTop - clientHeight <= SCROLL_THRESHOLD;

      if (isNearBottom) {
        void loadItems(nextPageUrl, searchTerm);
      }

      scrollTimeoutRef.current = null;
    }, 100);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setNextPageUrl('');
      void loadItems('', value);
    }, config.searchDebounceMs || SEARCH_DEBOUNCE_MS);
  };

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  const options: SelectOption[] = items.map((item, index) => ({
    ...config.mapToOption(item),
    key: config.getItemKey ? config.getItemKey(item) : `${config.mapToOption(item).value}-${index}`,
  }));

  return (
    <Select<string>
      allowClear
      value={value}
      onChange={onChange}
      onDropdownVisibleChange={(open) => open && handleDropdownOpen()}
      onPopupScroll={handleScroll}
      showSearch
      placeholder={config.placeholder}
      optionFilterProp="searchValue"
      options={options}
      loading={isLoading}
      onSearch={config.searchData ? handleSearch : undefined}
      filterOption={
        config.searchData
          ? false
          : (input, option) => {
              const searchValue = option?.searchValue?.toLowerCase() || '';
              return searchValue.includes(input.toLowerCase());
            }
      }
      notFoundContent={isLoading ? config.loadingText : config.notFoundText}
      {...props}
    />
  );
}

export default InfiniteSelect;

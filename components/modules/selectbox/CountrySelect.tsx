'use client';

import { SelectProps } from 'antd';
import React from 'react';
import { getCountries, searchCountries } from '@/app/services/location.service';
import { CountryResponse } from '@/types/location.types';
import InfiniteSelect, { InfiniteSelectConfig } from '@/components/modules/common/InfiniteSelect';

interface CountrySelectProps extends SelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

const CountrySelect = ({ value, onChange, ...props }: CountrySelectProps) => {
  const config: InfiniteSelectConfig<CountryResponse> = {
    mapToOption: (country) => ({
      value: country.iso2,
      label: `${country.emoji} ${country.name}`,
      searchValue: `${country.name} ${country.iso2} ${country.iso3}`,
    }),
    loadData: getCountries,
    searchData: searchCountries,
    searchDebounceMs: 500,
    placeholder: '<PERSON><PERSON><PERSON> seç<PERSON>',
    notFoundText: '<PERSON><PERSON><PERSON> bulunamadı',
    loadingText: 'Yükleniyor...',
  };

  return (
    <InfiniteSelect<CountryResponse> config={config} value={value} onChange={onChange} {...props} />
  );
};

export default CountrySelect;

import { Select, SelectProps } from 'antd';

const StatusSelect = (props: SelectProps) => {
  const options = [
    {
      value: 'active',
      label: 'Aktif',
    },
    {
      value: 'inactive',
      label: 'Pasif',
    },
    {
      value: 'pending',
      label: 'Onay Bekliyor',
    },
  ];
  return (
    <Select
      showSearch
      optionFilterProp="label"
      filterOption={(input, option) =>
        typeof option?.label === 'string' &&
        option.label.toLowerCase().includes(input.toLowerCase())
      }
      options={options}
      {...props}
    />
  );
};

export default StatusSelect;

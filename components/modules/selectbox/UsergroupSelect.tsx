import { Select, SelectProps } from 'antd';
import { UsergroupResponse } from '@/types/usergroup.types';

interface UsergroupSelectProps extends SelectProps {
  usergroups: UsergroupResponse[];
}

const UsergroupSelect = ({ usergroups, ...props }: UsergroupSelectProps) => {
  const options = (usergroups || []).map((usergroup) => ({
    value: usergroup.id,
    label: usergroup.name,
  }));

  return (
    <Select
      showSearch
      placeholder="Personel grubu seçiniz"
      filterOption={(inputValue, option) => {
        const label = option?.label?.toString().toLowerCase() || '';
        return label.includes(inputValue.toLowerCase());
      }}
      options={options}
      {...props}
    />
  );
};

export default UsergroupSelect;

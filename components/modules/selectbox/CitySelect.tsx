'use client';

import { SelectProps } from 'antd';
import React from 'react';
import { getCities, searchCities } from '@/app/services/location.service';
import { CityResponse } from '@/types/location.types';
import InfiniteSelect, { InfiniteSelectConfig } from '@/components/modules/common/InfiniteSelect';

interface CitySelectProps extends SelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

const CitySelect = ({ value, onChange, ...props }: CitySelectProps) => {
  const config: InfiniteSelectConfig<CityResponse> = {
    mapToOption: (city) => ({
      value: city.id.toString(),
      label: `${city.name}, ${city.state_name}`,
      searchValue: `${city.name} ${city.state_name} ${city.country_name} ${city.state_code} ${city.country_code}`,
    }),
    loadData: getCities,
    searchData: searchCities,
    searchDebounceMs: 500,
    placeholder: 'Şehir seçiniz',
    notFoundText: 'Şehir bulunamadı',
    loadingText: 'Yükleniyor...',
  };

  return (
    <InfiniteSelect<CityResponse> config={config} value={value} onChange={onChange} {...props} />
  );
};

export default CitySelect;

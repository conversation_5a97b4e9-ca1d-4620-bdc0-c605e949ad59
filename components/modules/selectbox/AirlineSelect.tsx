'use client';

import { SelectProps } from 'antd';
import InfiniteSelect, { InfiniteSelectConfig } from '@/components/modules/common/InfiniteSelect';
import { AirlineResponse } from '@/types/airline.types';
import { getAirlines, searchAirlines } from '@/app/services/airline.service';

type AirlineSelectProps = Omit<SelectProps, 'options' | 'loading'> & {
  value?: string;
  onChange?: (value: string) => void;
};

const AirlineSelect = ({ value, onChange, ...props }: AirlineSelectProps) => {
  const config: InfiniteSelectConfig<AirlineResponse> = {
    mapToOption: (airline) => ({
      value: airline.airline_code,
      label: `${airline.airline_name} (${airline.airline_code})`,
      searchValue: `${airline.airline_name} ${airline.airline_code}`,
    }),
    loadData: getAirlines,
    searchData: searchAirlines,
    searchDebounceMs: 500,
    placeholder: '<PERSON><PERSON><PERSON><PERSON> seçiniz',
    notFoundText: 'Havayolu bulunamadı',
    loadingText: 'Yükleniyor...',
  };

  return (
    <InfiniteSelect<AirlineResponse>
      config={config}
      value={value}
      onChange={onChange}
      optionLabelProp="label"
      {...props}
    />
  );
};

export default AirlineSelect;

import { Select, SelectProps } from 'antd';

const CompanyChangeSelect = (props: SelectProps) => {
  const options = [
    {
      value: '1',
      label: 'A Firması',
    },
    {
      value: '2',
      label: '<PERSON> Firması',
    },
    {
      value: '3',
      label: '<PERSON> Firması',
    },
    {
      value: '4',
      label: '<PERSON> Firması',
    },
    {
      value: '5',
      label: 'E Firması',
    },
    {
      value: '6',
      label: 'F Firması',
    },
    {
      value: '7',
      label: 'G Firması',
    },
    {
      value: '8',
      label: '<PERSON>has Firması',
    },
    {
      value: '9',
      label: '<PERSON> Firması',
    },
    {
      value: '10',
      label: 'J Firması',
    },
    {
      value: '11',
      label: <span>K Firması</span>,
    },
  ];

  return <Select {...props} options={options} />;
};

export default CompanyChangeSelect;

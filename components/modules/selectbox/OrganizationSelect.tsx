import { Select, SelectProps } from 'antd';
import { OrganizationResponse } from '@/types/organization.type';

interface OrganizationSelectProps extends SelectProps {
  organizations: OrganizationResponse[];
}

const OrganizationSelect = ({ organizations, ...props }: OrganizationSelectProps) => {
  const options = (organizations || []).map((organization) => ({
    value: organization.id,
    label: organization.name,
  }));

  return (
    <Select
      showSearch
      placeholder="Organizasyon seçiniz"
      filterOption={(inputValue, option) => {
        const label = option?.label?.toString().toLowerCase() || '';
        return label.includes(inputValue.toLowerCase());
      }}
      options={options}
      {...props}
    />
  );
};

export default OrganizationSelect;

import { Select, SelectProps } from 'antd';
import { UserResponse } from '@/types/user.types';

interface UserSelectProps extends SelectProps {
  users: UserResponse[];
}

const UserSelect = ({ users, ...props }: UserSelectProps) => {
  const options = (users || []).map((user) => ({
    value: user.id,
    label: `${user.first_name} ${user.last_name} (${user.email})`,
  }));

  return (
    <Select
      showSearch
      placeholder="Üst kullanıcı seçiniz"
      filterOption={(inputValue, option) => {
        const label = option?.label?.toString().toLowerCase() || '';
        return label.includes(inputValue.toLowerCase());
      }}
      options={options}
      {...props}
    />
  );
};

export default UserSelect;

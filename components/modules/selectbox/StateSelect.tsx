'use client';

import { SelectProps } from 'antd';
import React from 'react';
import { getStates, searchStates } from '@/app/services/location.service';
import { StateResponse } from '@/types/location.types';
import InfiniteSelect, { InfiniteSelectConfig } from '@/components/modules/common/InfiniteSelect';

interface StateSelectProps extends SelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

const StateSelect = ({ value, onChange, ...props }: StateSelectProps) => {
  const config: InfiniteSelectConfig<StateResponse> = {
    mapToOption: (state) => ({
      value: state.id.toString(),
      label: `${state.name}, ${state.country_name}`,
      searchValue: `${state.name} ${state.country_name} ${state.state_code} ${state.country_code}`,
    }),
    loadData: getStates,
    searchData: searchStates,
    searchDebounceMs: 500,
    placeholder: 'Eyalet/İl seçiniz',
    notFoundText: 'Eyalet/İl bulunamadı',
    loadingText: 'Yükleniyor...',
  };

  return (
    <InfiniteSelect<StateResponse> config={config} value={value} onChange={onChange} {...props} />
  );
};

export default StateSelect;

'use client';

import { SelectProps } from 'antd';
import React from 'react';
import { getCountries, searchCountries } from '@/app/services/location.service';
import { CountryResponse } from '@/types/location.types';
import InfiniteSelect, { InfiniteSelectConfig } from '@/components/modules/common/InfiniteSelect';

interface PhoneCodeSelectProps extends SelectProps {
  value?: string;
  onChange?: (value: string) => void;
}

const PhoneCodeSelect = ({ value, onChange, ...props }: PhoneCodeSelectProps) => {
  const config: InfiniteSelectConfig<CountryResponse> = {
    mapToOption: (country) => ({
      value: country.phone_code,
      label: `${country.emoji} +${country.phone_code} (${country.name})`,
      searchValue: `${country.name} ${country.phone_code} ${country.iso2} ${country.iso3}`,
    }),
    getItemKey: (country) => country.id.toString(),
    loadData: getCountries,
    searchData: searchCountries,
    searchDebounceMs: 500,
    placeholder: 'Telefon kodu seçiniz',
    notFoundText: 'Telefon kodu bulunamadı',
    loadingText: 'Yükleniyor...',
  };

  return (
    <InfiniteSelect<CountryResponse> config={config} value={value} onChange={onChange} {...props} />
  );
};

export default PhoneCodeSelect;

import { Select, SelectProps } from 'antd';

type GenderSelectProps = SelectProps;

const genderOptions = [
  { value: 'male', label: '<PERSON><PERSON><PERSON>' },
  { value: 'female', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'other', label: '<PERSON><PERSON><PERSON>' },
];

const GenderSelect = ({ ...props }: GenderSelectProps) => {
  return <Select placeholder="Cinsiyet seçiniz" options={genderOptions} {...props} />;
};

export default GenderSelect;

import { TreeSelect, TreeSelectProps } from 'antd';
import { JobtitleResponse } from '@/types/jobtitle.types';

type Props = TreeSelectProps;

interface JobTitleListSelectProps {
  jobtitles: JobtitleResponse[];
}

interface TreeItem {
  id: number;
  name: string;
  parent_id: number | null;
  children: TreeItem[];
}

interface TreeSelectDataNode {
  value: number;
  title: string;
  children?: TreeSelectDataNode[];
}

const JobTitleListSelect = ({ jobtitles, ...props }: Props & JobTitleListSelectProps) => {
  const buildTreeData = (items: JobtitleResponse[]) => {
    if (!items) return [];

    const itemMap: { [key: number]: TreeItem } = {};
    const rootNodes: TreeItem[] = [];

    // Populate itemMap with all items and initialize children arrays
    items.forEach(item => {
      itemMap[item.id] = { ...item, children: [] };
    });

    // Link children to their parents
    items.forEach(item => {
      if (item.parent_id === null || !itemMap[item.parent_id]) {
        rootNodes.push(itemMap[item.id]);
      } else {
        // Parent found, add to parent's children
        itemMap[item.parent_id].children.push(itemMap[item.id]);
      }
    });

    // Recursively sort children alphabetically by name
    const sortChildren = (nodes: TreeItem[]) => {
      nodes.forEach(node => {
        if (node.children.length > 0) {
          node.children.sort((a, b) => a.name.localeCompare(b.name));
          sortChildren(node.children);
        }
      });
    };

    // Sort top-level nodes alphabetically by name
    rootNodes.sort((a, b) => a.name.localeCompare(b.name));

    // Sort children of all nodes recursively starting from roots
    sortChildren(rootNodes);

    // Recursively map the internal tree structure to TreeSelect format
    const mapToTreeSelectFormat = (nodes: TreeItem[]): TreeSelectDataNode[] => {
      return nodes.map(node => ({
        value: node.id,
        title: node.name,
        children: node.children.length > 0
          ? mapToTreeSelectFormat(node.children)
          : undefined, // Use undefined for nodes without children
      }));
    };

    return mapToTreeSelectFormat(rootNodes);
  };

  return (
    <TreeSelect
      showSearch
      treeData={buildTreeData(jobtitles)}
      {...props}
    />
  );
};

export default JobTitleListSelect;

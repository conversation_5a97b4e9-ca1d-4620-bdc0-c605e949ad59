'use client';

import { Button, Form, Input, Switch } from 'antd';
import { ReactNode } from 'react';

interface HierarchicalFormFieldsProps {
  nameLabel: string;
  namePlaceholder: string;
  descriptionPlaceholder: string;
  parentLabel: string;
  parentPlaceholder: string;
  parentSelectComponent: ReactNode;
  hasParent: boolean;
  onParentChange: (checked: boolean) => void;
  submitButtonText: string;
  onCancel?: () => void;
}

const HierarchicalFormFields = ({
  nameLabel,
  namePlaceholder,
  descriptionPlaceholder,
  parentLabel,
  parentSelectComponent,
  hasParent,
  onParentChange,
  submitButtonText,
  onCancel,
}: HierarchicalFormFieldsProps) => {
  return (
    <>
      <Form.Item
        label={nameLabel}
        name="name"
        rules={[{ required: true, message: `${nameLabel} zorunludur.` }]}
      >
        <Input placeholder={namePlaceholder} />
      </Form.Item>

      <Form.Item label="Açıklama" name="description">
        <Input.TextArea rows={3} placeholder={descriptionPlaceholder} />
      </Form.Item>

      <Form.Item label={`${parentLabel} Var mı?`}>
        <Switch checked={hasParent} onChange={onParentChange} />
      </Form.Item>

      {hasParent && (
        <Form.Item
          label={parentLabel}
          name="parent_id"
          rules={[{ required: true, message: `${parentLabel} seçimi zorunludur.` }]}
        >
          {parentSelectComponent}
        </Form.Item>
      )}

      <Form.Item>
        <Button type="primary" htmlType="submit">
          {submitButtonText}
        </Button>
        {onCancel && (
          <Button className="ml-2" onClick={onCancel}>
            İptal
          </Button>
        )}
      </Form.Item>
    </>
  );
};

export default HierarchicalFormFields;

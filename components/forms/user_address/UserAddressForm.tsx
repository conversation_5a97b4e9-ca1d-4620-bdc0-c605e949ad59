'use client';

import { useEffect } from 'react';
import { Button, Form, Input, Select, Space, Switch } from 'antd';
import { useRouter } from 'next/navigation';
import { useFormHandler } from '@/hooks/useFormHandler';
import { createUserAddress, updateUserAddress } from '@/app/services/user_address.service';
import { UserAddressCreateRequest, UserAddressResponse } from '@/types/user_address.types';
import { ROUTES } from '@/lib/routes';
import CountrySelect from '@/components/modules/selectbox/CountrySelect';

interface UserAddressFormProps {
  mode: 'create' | 'update';
  userAddress?: UserAddressResponse;
}

const UserAddressForm = ({ mode, userAddress }: UserAddressFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler<UserAddressResponse>(form);

  useEffect(() => {
    if (mode === 'update' && userAddress) {
      form.setFieldsValue({
        type: userAddress.type,
        address_line_1: userAddress.address_line_1,
        address_line_2: userAddress.address_line_2,
        country_code: userAddress.country_code,
        city: userAddress.city,
        neighborhood_street: userAddress.neighborhood_street,
        postal_code: userAddress.postal_code,
        is_default: userAddress.is_default,
      });
    }
  }, [form, mode, userAddress]);

  const onFinish = async (values: UserAddressCreateRequest) => {
    console.log('Form values received:', values);

    const payload: UserAddressCreateRequest = {
      type: values.type,
      address_line_1: values.address_line_1,
      address_line_2: values.address_line_2,
      country_code: values.country_code,
      city: values.city,
      neighborhood_street: values.neighborhood_street,
      postal_code: values.postal_code,
      is_default: values.is_default || false,
    };

    console.log('Payload to send:', payload);

    let res;
    if (mode === 'create') {
      res = await createUserAddress(payload);
    } else {
      res = await updateUserAddress(userAddress!.id, payload);
    }

    console.log('API Response:', res);
    formHandle(res, () => {
      router.push(ROUTES.PROFILE.ADDRESS.INDEX);
    });
  };

  const addressTypeOptions = [
    { label: 'Ev', value: 'home' },
    { label: 'İş', value: 'work' },
    { label: 'Diğer', value: 'other' },
  ];

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Adres Türü"
        name="type"
        rules={[{ required: true, message: 'Adres türü seçiniz.' }]}
      >
        <Select placeholder="Seçiniz" options={addressTypeOptions} />
      </Form.Item>

      <Form.Item
        label="Adres 1"
        name="address_line_1"
        rules={[{ required: true, message: 'Adres 1 zorunludur.' }]}
      >
        <Input placeholder="Ana adres bilgisini giriniz" />
      </Form.Item>

      <Form.Item label="Adres 2" name="address_line_2">
        <Input placeholder="Ek adres bilgisini giriniz (opsiyonel)" />
      </Form.Item>

      <Form.Item
        label="Ülke Kodu"
        name="country_code"
        rules={[{ required: true, message: 'Ülke kodu giriniz.' }]}
      >
        <CountrySelect />
      </Form.Item>

      <Form.Item label="Şehir" name="city">
        <Input placeholder="Şehir giriniz" />
      </Form.Item>

      <Form.Item label="Mahalle/Sokak" name="neighborhood_street">
        <Input placeholder="Mahalle/Sokak bilgisini giriniz" />
      </Form.Item>

      <Form.Item label="Posta Kodu" name="postal_code">
        <Input placeholder="Posta kodunu giriniz" />
      </Form.Item>

      <Form.Item label="Varsayılan Adres" name="is_default" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {mode === 'create' ? 'Kaydet' : 'Güncelle'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default UserAddressForm;

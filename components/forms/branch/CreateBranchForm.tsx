'use client';

import { Form } from 'antd';
import Branch<PERSON>ormFields from './BranchFormFields';
import { BranchCreateRequest } from '@/types/branch.types';
import { createBranch } from '@/app/services/branch.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

const CreateBranchForm = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const onFinish = async (values: BranchCreateRequest) => {
    const res = await createBranch(values);
    formHandle(res, () => {
      router.push(ROUTES.BRANCH.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <BranchFormFields submitButtonText="Şube Ekle" />
    </Form>
  );
};

export default CreateBranchForm;

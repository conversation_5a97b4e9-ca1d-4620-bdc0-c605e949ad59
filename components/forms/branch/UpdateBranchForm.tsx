'use client';

import { useEffect } from 'react';
import { Form } from 'antd';
import BranchFormFields from './BranchFormFields';
import { BranchCreateRequest, BranchResponse } from '@/types/branch.types';
import { updateBranch } from '@/app/services/branch.service';
import { useForm<PERSON>andler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

interface UpdateBranchFormProps {
  branch: BranchResponse;
}

const UpdateBranchForm = ({ branch }: UpdateBranchFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  useEffect(() => {
    form.setFieldsValue({
      address_name: branch.address_name,
      address_type: branch.address_type,
      address_1: branch.address_1,
      address_2: branch.address_2,
      accounting_number: branch.accounting_number,
      country: branch.country,
      city: branch.city,
      neighborhood: branch.neighborhood,
      postal_code: branch.postal_code,
      tax_number: branch.tax_number,
      tax_office: branch.tax_office,
      mersis_no: branch.mersis_no,
      trade_registry_no: branch.trade_registry_no,
      country_code_phone: branch.country_code_phone,
      phone_number: branch.phone_number,
      country_code_fax: branch.country_code_fax,
      fax_number: branch.fax_number,
    });
  }, [form, branch]);

  const onFinish = async (values: BranchCreateRequest) => {
    const res = await updateBranch(branch.id, values);
    formHandle(res, () => {
      router.push(ROUTES.BRANCH.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <BranchFormFields submitButtonText="Güncelle" />
    </Form>
  );
};

export default UpdateBranchForm;

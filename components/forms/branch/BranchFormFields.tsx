'use client';

import { Button, Card, Form, Input, Select, Space } from 'antd';
import CountrySelect from '@/components/modules/selectbox/CountrySelect';
import CitySelect from '@/components/modules/selectbox/CitySelect';

interface BranchFormFieldsProps {
  submitButtonText: string;
}

const BranchFormFields = ({ submitButtonText }: BranchFormFieldsProps) => {
  const addressTypeOptions = [
    { value: 'merkez', label: 'Merkez' },
    { value: 'sube', label: 'Şube' },
    { value: 'depo', label: 'Depo' },
    { value: 'ofis', label: 'Ofis' },
  ];

  const countryCodeOptions = [
    { value: '+90', label: '+90 (T<PERSON>rk<PERSON><PERSON>)' },
    { value: '+1', label: '+1 (ABD/Kanada)' },
    { value: '+44', label: '+44 (İngiltere)' },
    { value: '+49', label: '+49 (Almanya)' },
    { value: '+33', label: '+33 (<PERSON><PERSON><PERSON>)' },
  ];

  return (
    <div className="flex flex-col gap-4">
      {/* BÖLÜM 1: ADRES VE KONUM BİLGİLERİ */}
      <Card className="mb-4 space-y-6 shadow-sm">
        <div className="mb-4 border-b border-gray-200 pb-4">
          <h3 className="text-lg font-medium text-blue-500">Adres ve Konum Bilgileri</h3>
          <p className="text-sm text-gray-500">Şubenin adres ve konum bilgilerini giriniz.</p>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item
            label="Adres Tanımı"
            name="address_name"
            rules={[{ required: true, message: 'Adres tanımı giriniz.' }]}
          >
            <Input placeholder="Adres tanımını giriniz" />
          </Form.Item>

          <Form.Item
            label="Adres Türü"
            name="address_type"
            rules={[{ required: true, message: 'Adres türü seçiniz.' }]}
          >
            <Select placeholder="Adres türü seçiniz" options={addressTypeOptions} />
          </Form.Item>
        </div>

        <Form.Item
          label="Adres 1"
          name="address_1"
          rules={[{ required: true, message: 'Adres 1 giriniz.' }]}
        >
          <Input placeholder="Ana adres bilgisini giriniz" />
        </Form.Item>

        <Form.Item label="Adres 2" name="address_2">
          <Input placeholder="Ek adres bilgisini giriniz (opsiyonel)" />
        </Form.Item>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <Form.Item
            label="Ülke"
            name="country"
            rules={[{ required: true, message: 'Ülke seçiniz.' }]}
          >
            <CountrySelect />
          </Form.Item>

          <Form.Item label="Şehir" name="city">
            <CitySelect />
          </Form.Item>

          <Form.Item label="Posta Kodu" name="postal_code">
            <Input placeholder="Posta kodunu giriniz" />
          </Form.Item>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item label="Mahalle/Sokak" name="neighborhood">
            <Input placeholder="Mahalle veya sokak adını giriniz" />
          </Form.Item>

          <Form.Item label="Muhasebe Numarası" name="accounting_number">
            <Input placeholder="Muhasebe numarasını giriniz" />
          </Form.Item>
        </div>
      </Card>

      {/* BÖLÜM 2: VERGİ VE TİCARET BİLGİLERİ */}
      <Card className="mt-4 space-y-6 shadow-sm">
        <div className="mb-4 border-b border-gray-200 pb-4">
          <h3 className="text-lg font-medium text-blue-500">Vergi ve Ticaret Bilgileri</h3>
          <p className="text-sm text-gray-500">Şubenin vergi ve ticaret bilgilerini giriniz.</p>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item label="Vergi No" name="tax_number">
            <Input placeholder="Vergi numarasını giriniz" />
          </Form.Item>

          <Form.Item label="Vergi Dairesi" name="tax_office">
            <Input placeholder="Vergi dairesini giriniz" />
          </Form.Item>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item label="Mersis No" name="mersis_no">
            <Input placeholder="Mersis numarasını giriniz" />
          </Form.Item>

          <Form.Item label="Ticaret Sicil No" name="trade_registry_no">
            <Input placeholder="Ticaret sicil numarasını giriniz" />
          </Form.Item>
        </div>
      </Card>

      {/* BÖLÜM 3: İLETİŞİM BİLGİLERİ */}
      <Card className="mt-4 space-y-6 shadow-sm">
        <div className="mb-4 border-b border-gray-200 pb-4">
          <h3 className="text-lg font-medium text-blue-500">İletişim Bilgileri</h3>
          <p className="text-sm text-gray-500">Şubenin telefon ve faks bilgilerini giriniz.</p>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item label="Ülke Kodu (Telefon)" name="country_code_phone">
            <Select placeholder="Ülke kodu seçiniz" options={countryCodeOptions} />
          </Form.Item>

          <Form.Item label="Telefon Numarası" name="phone_number">
            <Input placeholder="Telefon numarasını giriniz" />
          </Form.Item>
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item label="Ülke Kodu (Faks)" name="country_code_fax">
            <Select placeholder="Ülke kodu seçiniz" options={countryCodeOptions} />
          </Form.Item>

          <Form.Item label="Faks Numarası" name="fax_number">
            <Input placeholder="Faks numarasını giriniz" />
          </Form.Item>
        </div>
      </Card>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {submitButtonText}
          </Button>
        </Space>
      </Form.Item>
    </div>
  );
};

export default BranchFormFields;

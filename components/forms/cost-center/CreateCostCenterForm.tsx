'use client';

import { Form } from 'antd';
import CostCenterFormFields from './CostCenterFormFields';
import { CostCenterCreateRequest } from '@/types/costcenter.types';
import { DepartmentResponse } from '@/types/department.types';
import { JobtitleResponse } from '@/types/jobtitle.types';
import { createCostCenter } from '@/app/services/constcenter.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

interface CreateCostCenterFormProps {
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
}

const CreateCostCenterForm = ({ departments, jobtitles }: CreateCostCenterFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const onFinish = async (values: CostCenterCreateRequest) => {
    console.log('Form values:', values);
    const res = await createCostCenter(values);
    console.log('API response:', res);
    formHandle(res, () => {
      router.push(ROUTES.COSTCENTER.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <CostCenterFormFields
        departments={departments}
        jobtitles={jobtitles}
        submitButtonText="Masraf Merkezi Ekle"
      />
    </Form>
  );
};

export default CreateCostCenterForm;

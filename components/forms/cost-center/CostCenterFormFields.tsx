'use client';

import { Button, Form, Input } from 'antd';
import DepartmentMultiSelect from '@/components/modules/selectbox/DepartmentMultiSelect';
import JobtitleMultiSelect from '@/components/modules/selectbox/JobtitleMultiSelect';
import { DepartmentResponse } from '@/types/department.types';
import { JobtitleResponse } from '@/types/jobtitle.types';

interface CostCenterFormFieldsProps {
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
  submitButtonText: string;
  onCancel?: () => void;
}

const CostCenterFormFields = ({
  departments,
  jobtitles,
  submitButtonText,
  onCancel,
}: CostCenterFormFieldsProps) => {
  return (
    <>
      <Form.Item
        label="Masraf Merkezi Adı"
        name="name"
        rules={[{ required: true, message: 'Masraf merkezi adı zorunludur.' }]}
      >
        <Input placeholder="Masraf merkezi adını giriniz" />
      </Form.Item>

      <Form.Item
        label="Masraf Merkez<PERSON>"
        name="code"
        rules={[{ required: true, message: 'Masraf merkezi kodu zorunludur.' }]}
      >
        <Input placeholder="Masraf merkezi kodunu giriniz" />
      </Form.Item>

      <Form.Item label="Departmanlar (Opsiyonel)" name="department_ids">
        <DepartmentMultiSelect departments={departments} />
      </Form.Item>

      <Form.Item label="Pozisyonlar (Opsiyonel)" name="jobtitle_ids">
        <JobtitleMultiSelect jobtitles={jobtitles} />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          {submitButtonText}
        </Button>
        {onCancel && (
          <Button className="ml-2" onClick={onCancel}>
            İptal
          </Button>
        )}
      </Form.Item>
    </>
  );
};

export default CostCenterFormFields;

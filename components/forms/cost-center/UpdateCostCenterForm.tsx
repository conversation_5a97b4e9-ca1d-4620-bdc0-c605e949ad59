'use client';

import { Form } from 'antd';
import CostCenterFormFields from './CostCenterFormFields';
import { CostCenterResponse, CostCenterUpdateRequest } from '@/types/costcenter.types';
import { DepartmentResponse } from '@/types/department.types';
import { JobtitleResponse } from '@/types/jobtitle.types';
import { updateCostCenter } from '@/app/services/constcenter.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useEffect } from 'react';

interface UpdateCostCenterFormProps {
  costCenter: CostCenterResponse;
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
}

const UpdateCostCenterForm = ({
  costCenter,
  departments,
  jobtitles,
}: UpdateCostCenterFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  useEffect(() => {
    form.setFieldsValue({
      name: costCenter.name,
      code: costCenter.code,
      department_ids: costCenter.departments?.map((dept) => dept.id) || [],
      jobtitle_ids: costCenter.jobtitles?.map((job) => job.id) || [],
    });
  }, [form, costCenter]);

  const onFinish = async (values: CostCenterUpdateRequest) => {
    const res = await updateCostCenter(costCenter.id, values);
    formHandle(res, () => {
      router.push(ROUTES.COSTCENTER.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <CostCenterFormFields
        departments={departments}
        jobtitles={jobtitles}
        submitButtonText="Masraf Merkezi Güncelle"
        onCancel={() => router.push(ROUTES.COSTCENTER.INDEX)}
      />
    </Form>
  );
};

export default UpdateCostCenterForm;

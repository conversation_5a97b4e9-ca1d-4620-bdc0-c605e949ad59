'use client';

import { useEffect } from 'react';
import { Button, Form, Input, Select, Space, Switch } from 'antd';
import { useRouter } from 'next/navigation';
import { useFormHandler } from '@/hooks/useFormHandler';
import { createUserPhone, updateUserPhone } from '@/app/services/user_phone.service';
import { UserPhoneCreateRequest, UserPhoneResponse } from '@/types/user_phone.types';
import { ROUTES } from '@/lib/routes';
import CountrySelect from '@/components/modules/selectbox/CountrySelect';

interface UserPhoneFormProps {
  mode: 'create' | 'update';
  userPhone?: UserPhoneResponse;
}

const UserPhoneForm = ({ mode, userPhone }: UserPhoneFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler<UserPhoneResponse>(form);

  useEffect(() => {
    if (mode === 'update' && userPhone) {
      form.setFieldsValue({
        type: userPhone.type,
        country_code: userPhone.country_code,
        phone_number: userPhone.phone_number,
        is_default: userPhone.is_default,
      });
    }
  }, [form, mode, userPhone]);

  const onFinish = async (values: UserPhoneCreateRequest) => {
    console.log('Form values received:', values);

    const payload: UserPhoneCreateRequest = {
      type: values.type,
      country_code: values.country_code,
      phone_number: values.phone_number,
      is_default: values.is_default || false,
    };

    console.log('Payload to send:', payload);

    let res;
    if (mode === 'create') {
      res = await createUserPhone(payload);
    } else {
      res = await updateUserPhone(userPhone!.id, payload);
    }

    console.log('API Response:', res);
    formHandle(res, () => {
      router.push(ROUTES.PROFILE.PHONE.INDEX);
    });
  };

  const phoneTypeOptions = [
    { label: 'Ev', value: 'home' },
    { label: 'Cep', value: 'mobile' },
    { label: 'Acil', value: 'emergency' },
  ];

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Telefon Türü"
        name="type"
        rules={[{ required: true, message: 'Telefon türü seçiniz.' }]}
      >
        <Select placeholder="Seçiniz" options={phoneTypeOptions} />
      </Form.Item>

      <Form.Item
        label="Ülke Kodu"
        name="country_code"
        rules={[{ required: true, message: 'Ülke kodu giriniz.' }]}
      >
        <CountrySelect />
      </Form.Item>

      <Form.Item
        label="Telefon Numarası"
        name="phone_number"
        rules={[{ required: true, message: 'Telefon numarası zorunludur.' }]}
      >
        <Input placeholder="Telefon numarasını giriniz" />
      </Form.Item>

      <Form.Item label="Varsayılan Numara" name="is_default" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {mode === 'create' ? 'Kaydet' : 'Güncelle'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default UserPhoneForm;

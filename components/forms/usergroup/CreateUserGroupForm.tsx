'use client';

import { Form } from 'antd';
import UserGroupFormFields from './UserGroupFormFields';
import { UsergroupCreateRequest } from '@/types/usergroup.types';
import { createUsergroup } from '@/app/services/usergroup.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

const CreateUserGroupForm = () => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();

  const onFinish = async (values: UsergroupCreateRequest) => {
    const res = await createUsergroup(values);
    formHandle(res, () => {
      router.push(ROUTES.USERGROUP.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <UserGroupFormFields submitButtonText="Kaydet" />
    </Form>
  );
};

export default CreateUserGroupForm;

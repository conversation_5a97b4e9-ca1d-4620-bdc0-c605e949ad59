'use client';

import { Button, Form, Input } from 'antd';

interface UserGroupFormFieldsProps {
  submitButtonText: string;
  onCancel?: () => void;
}

const UserGroupFormFields = ({ submitButtonText, onCancel }: UserGroupFormFieldsProps) => {
  return (
    <>
      <Form.Item
        label="Grup Adı"
        name="name"
        rules={[
          { required: true, message: 'Grup adı zorunludur' },
          { min: 2, message: 'En az 2 karakter olmalı' },
          { max: 50, message: 'En fazla 50 karakter olabilir' },
        ]}
      >
        <Input placeholder="Personel grubu adını giriniz" />
      </Form.Item>

      <Form.Item label="Açıklama" name="description">
        <Input.TextArea rows={3} placeholder="Grup açıklamasını giriniz (opsiyonel)" />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          {submitButtonText}
        </Button>
        {onCancel && (
          <Button className="ml-2" onClick={onCancel}>
            İptal
          </Button>
        )}
      </Form.Item>
    </>
  );
};

export default UserGroupFormFields;

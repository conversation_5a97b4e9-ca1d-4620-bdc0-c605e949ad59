'use client';

import { Form } from 'antd';
import UserGroupFormFields from './UserGroupFormFields';
import { useEffect } from 'react';
import { UsergroupCreateRequest, UsergroupResponse } from '@/types/usergroup.types';
import { updateUsergroup } from '@/app/services/usergroup.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

interface UpdateUserGroupFormProps {
  usergroup: UsergroupResponse;
}

const UpdateUserGroupForm = ({ usergroup }: UpdateUserGroupFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();

  useEffect(() => {
    // Form'u usergroup verileri ile doldur
    form.setFieldsValue({
      name: usergroup.name,
      description: usergroup.description,
    });
  }, [usergroup, form]);

  const onFinish = async (values: UsergroupCreateRequest) => {
    const res = await updateUsergroup(usergroup.id, values);
    formHandle(res, () => {
      router.push(ROUTES.USERGROUP.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <UserGroupFormFields
        submitButtonText="Güncelle"
        onCancel={() => router.push(ROUTES.USERGROUP.INDEX)}
      />
    </Form>
  );
};

export default UpdateUserGroupForm;

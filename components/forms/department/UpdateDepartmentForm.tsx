'use client';

import { Form } from 'antd';
import { useEffect, useState } from 'react';
import { DepartmentCreateRequest, DepartmentResponse } from '@/types/department.types';
import { updateDepartment } from '@/app/services/department.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import DepartmentListSelect from '@/components/modules/selectbox/DepartmentListSelect';
import HierarchicalFormFields from '@/components/forms/shared/HierarchicalFormFields';

interface UpdateDepartmentFormProps {
  department: DepartmentResponse;
  departments: DepartmentResponse[];
}

const UpdateDepartmentForm = ({ department, departments }: UpdateDepartmentFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();
  const [hasParent, setHasParent] = useState<boolean>(department.parent_id !== null);

  useEffect(() => {
    // Form'u department verileri ile doldur
    form.setFieldsValue({
      name: department.name,
      description: department.description,
      parent_id: department.parent_id,
    });
  }, [department, form]);

  const onFinish = async (values: DepartmentCreateRequest) => {
    const payload = {
      ...values,
      parent_id: hasParent ? values.parent_id : null,
    };

    const res = await updateDepartment(department.id, payload);
    formHandle(res, () => {
      router.push(ROUTES.DEPARTMENT.INDEX);
    });
  };

  const handleParentChange = (checked: boolean) => {
    setHasParent(checked);
    if (!checked) {
      form.setFieldValue('parent_id', null);
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <HierarchicalFormFields
        nameLabel="Departman Adı"
        namePlaceholder="Departman adını giriniz"
        descriptionPlaceholder="Departman açıklamasını giriniz (opsiyonel)"
        parentLabel="Üst Departman"
        parentPlaceholder="Üst departman seçiniz"
        parentSelectComponent={
          <DepartmentListSelect
            departments={departments}
            placeholder="Üst departman seçiniz"
            allowClear
          />
        }
        hasParent={hasParent}
        onParentChange={handleParentChange}
        submitButtonText="Güncelle"
        onCancel={() => router.push(ROUTES.DEPARTMENT.INDEX)}
      />
    </Form>
  );
};

export default UpdateDepartmentForm;

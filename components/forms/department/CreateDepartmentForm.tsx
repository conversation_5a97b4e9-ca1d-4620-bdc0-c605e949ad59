'use client';

import { Form } from 'antd';
import { useState } from 'react';
import { DepartmentCreateRequest, DepartmentResponse } from '@/types/department.types';
import { createDepartment } from '@/app/services/department.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import DepartmentListSelect from '@/components/modules/selectbox/DepartmentListSelect';
import HierarchicalFormFields from '@/components/forms/shared/HierarchicalFormFields';

interface CreateDepartmentFormProps {
  departments: DepartmentResponse[];
}

const CreateDepartmentForm = ({ departments }: CreateDepartmentFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();
  const [hasParent, setHasParent] = useState<boolean>(false);

  const onFinish = async (values: DepartmentCreateRequest) => {
    const payload = {
      ...values,
      parent_id: hasParent ? values.parent_id : null,
    };
    const res = await createDepartment(payload);
    formHandle(res, () => {
      router.push(ROUTES.DEPARTMENT.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <HierarchicalFormFields
        nameLabel="Departman Adı"
        namePlaceholder="Departman adını giriniz"
        descriptionPlaceholder="Departman açıklamasını giriniz (opsiyonel)"
        parentLabel="Üst Departman"
        parentPlaceholder="Üst departman seçiniz"
        parentSelectComponent={
          <DepartmentListSelect
            departments={departments}
            placeholder="Üst departman seçiniz"
            allowClear
          />
        }
        hasParent={hasParent}
        onParentChange={setHasParent}
        submitButtonText="Kaydet"
      />
    </Form>
  );
};

export default CreateDepartmentForm;

'use client';

import { Button, Form, Input, Select } from 'antd';

interface ReasonFormFieldsProps {
  submitButtonText: string;
  onCancel?: () => void;
}

const ReasonFormFields = ({ submitButtonText, onCancel }: ReasonFormFieldsProps) => {
  const reasonTypes = [
    { value: 'travel_reason', label: 'Seyahat Sebebi' },
    { value: 'violation_explanation', label: '<PERSON>hlal Açıklaması' },
    { value: 'rejection_reason', label: 'Red Edilme Nedeni' },
  ];

  return (
    <>
      <Form.Item
        label="Sebep Adı"
        name="name"
        rules={[{ required: true, message: 'Sebe<PERSON> adı zorunludur' }]}
      >
        <Input placeholder="Sebep adını giriniz" />
      </Form.Item>

      <Form.Item
        label="Sebep Tipi"
        name="type"
        rules={[{ required: true, message: 'Sebep tipi zorunludur' }]}
      >
        <Select placeholder="Sebep tipini seçiniz" options={reasonTypes} />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          {submitButtonText}
        </Button>
        {onCancel && (
          <Button className="ml-2" onClick={onCancel}>
            İptal
          </Button>
        )}
      </Form.Item>
    </>
  );
};

export default ReasonFormFields;

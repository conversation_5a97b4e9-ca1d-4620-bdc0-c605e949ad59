'use client';

import { Button, Form, Input, Select } from 'antd';
import { updateReason } from '@/app/services/reason.service';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { ReasonResponse, ReasonCreateRequest } from '@/types/reason.types';
import { useEffect } from 'react';
import { useFormHandler } from '@/hooks/useFormHandler';

interface UpdateReasonFormProps {
  reason: ReasonResponse;
}

const UpdateReasonForm = ({ reason }: UpdateReasonFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const reasonTypes = [
    { value: 'travel_reason', label: 'Seyahat Sebebi' },
    { value: 'violation_explanation', label: 'İhlal Açıklaması' },
    { value: 'rejection_reason', label: 'Red Edilme Nedeni' },
  ];

  useEffect(() => {
    form.setFieldsValue({
      name: reason.name,
      type: reason.type,
    });
  }, [reason, form]);

  const onFinish = async (values: ReasonCreateRequest) => {
    const res = await updateReason(reason.id, values);
    formHandle(res, () => {
      router.push(ROUTES.REASON.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Açıklama Türü"
        name="type"
        rules={[{ required: true, message: 'Açıklama türü zorunludur.' }]}
      >
        <Select
          placeholder="Açıklama türünü seçiniz"
          options={reasonTypes}
        />
      </Form.Item>

      <Form.Item
        label="Açıklama Adı"
        name="name"
        rules={[{ required: true, message: 'Açıklama adı zorunludur.' }]}
      >
        <Input placeholder="Açıklama adını giriniz" />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Güncelle
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UpdateReasonForm;

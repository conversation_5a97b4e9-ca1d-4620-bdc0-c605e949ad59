'use client';

import { Button, Form, Input, Select } from 'antd';
import { createReason } from '@/app/services/reason.service';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useFormHandler } from '@/hooks/useFormHandler';
import { ReasonCreateRequest } from '@/types/reason.types';

const CreateReasonForm = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const reasonTypes = [
    { value: 'travel_reason', label: 'Seyahat Sebebi' },
    { value: 'violation_explanation', label: 'İhlal Açıklaması' },
    { value: 'rejection_reason', label: 'Red Edilme Nedeni' },
  ];

  const onFinish = async (values: ReasonCreateRequest) => {
    const res = await createReason(values);
    formHandle(res, () => {
      router.push(ROUTES.REASON.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Açıklama Türü"
        name="type"
        rules={[{ required: true, message: 'Açıklama türü zorunludur.' }]}
      >
        <Select
          placeholder="Açıklama türünü seçiniz"
          options={reasonTypes}
        />
      </Form.Item>

      <Form.Item
        label="Açıklama Adı"
        name="name"
        rules={[{ required: true, message: 'Açıklama adı zorunludur.' }]}
      >
        <Input placeholder="Açıklama adını giriniz" />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Kaydet
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CreateReasonForm;

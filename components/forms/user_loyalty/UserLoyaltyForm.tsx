'use client';

import { useEffect } from 'react';
import { Button, DatePicker, Form, Input, Space, Switch } from 'antd';
import { useRouter } from 'next/navigation';
import { useFormHandler } from '@/hooks/useFormHandler';
import { createUserLoyalty, updateUserLoyalty } from '@/app/services/user_loyalty.service';
import {
  UserLoyaltyCreateRequest,
  UserLoyaltyFormData,
  UserLoyaltyResponse,
} from '@/types/user_loyalty.types';
import { ROUTES } from '@/lib/routes';
import dayjs from 'dayjs';
import AirlineSelect from '@/components/modules/selectbox/AirlineSelect';

interface UserLoyaltyFormProps {
  mode: 'create' | 'update';
  userLoyalty?: UserLoyaltyResponse;
}

const UserLoyaltyForm = ({ mode, userLoyalty }: UserLoyaltyFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler<UserLoyaltyResponse>(form);

  useEffect(() => {
    if (mode === 'update' && userLoyalty) {
      form.setFieldsValue({
        airline_id: userLoyalty.airline?.airline_code,
        card_number: userLoyalty.card_number,
        start_date: userLoyalty.start_date ? dayjs(userLoyalty.start_date) : null,
        end_date: userLoyalty.end_date ? dayjs(userLoyalty.end_date) : null,
        is_default: userLoyalty.is_default,
      });
    }
  }, [form, mode, userLoyalty]);

  const onFinish = async (values: UserLoyaltyFormData) => {
    const payload: UserLoyaltyCreateRequest = {
      airline_id: values.airline_id,
      card_number: values.card_number,
      start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : '',
      end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : '',
      is_default: values.is_default || false,
    };

    let res;
    if (mode === 'create') {
      res = await createUserLoyalty(payload);
    } else {
      res = await updateUserLoyalty(userLoyalty!.id, payload);
    }

    formHandle(res, () => {
      router.push(ROUTES.PROFILE.LOYALTY.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Havayolu"
        name="airline_id"
        rules={[{ required: true, message: 'Havayolu seçiniz.' }]}
      >
        <AirlineSelect />
      </Form.Item>

      <Form.Item
        label="Kart Numarası"
        name="card_number"
        rules={[{ required: true, message: 'Kart numarası zorunludur.' }]}
      >
        <Input placeholder="Kart numarasını giriniz" />
      </Form.Item>

      <Form.Item
        label="Başlangıç Tarihi"
        name="start_date"
        rules={[{ required: true, message: 'Başlangıç tarihi zorunludur.' }]}
      >
        <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full" />
      </Form.Item>

      <Form.Item
        label="Bitiş Tarihi"
        name="end_date"
        rules={[{ required: true, message: 'Bitiş tarihi zorunludur.' }]}
      >
        <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full" />
      </Form.Item>

      <Form.Item label="Varsayılan Sadakat Programı" name="is_default" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {mode === 'create' ? 'Kaydet' : 'Güncelle'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default UserLoyaltyForm;

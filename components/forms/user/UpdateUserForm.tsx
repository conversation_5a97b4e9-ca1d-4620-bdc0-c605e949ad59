'use client';

import { Button, Form, Input } from 'antd';
import JobTitleListSelect from '@/components/modules/selectbox/JobTitleListSelect';
import DepartmentListSelect from '@/components/modules/selectbox/DepartmentListSelect';
import StatusSelect from '@/components/modules/selectbox/StatusSelect';
import UsergroupSelect from '@/components/modules/selectbox/UsergroupSelect';
import UserSelect from '@/components/modules/selectbox/UserSelect';
import { UserUpdateRequest, UserResponse } from '@/types/user.types';
import { DepartmentResponse } from '@/types/department.types';
import { JobtitleResponse } from '@/types/jobtitle.types';
import { UsergroupResponse } from '@/types/usergroup.types';
import { updateUser } from '@/app/services/user.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useEffect } from 'react';

interface UpdateUserFormProps {
  user: UserResponse;
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
  usergroups: UsergroupResponse[];
  users: UserResponse[];
}

const UpdateUserForm = ({ user, departments, jobtitles, usergroups, users }: UpdateUserFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  useEffect(() => {
    form.setFieldsValue({
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      status: user.status,
      department_id: user.department?.id || null,
      jobtitle_id: user.job_title?.id || null,
      parent_user_id: user.parent_user?.id || null,
      usergroup_id: user.user_group?.id || null,
    });
  }, [form, user]);

  const onFinish = async (values: UserUpdateRequest) => {
    const res = await updateUser(user.id, values);
    formHandle(res, () => {
      router.push(ROUTES.USER.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Ad"
        name="first_name"
        rules={[{ required: true, message: 'Ad zorunludur.' }]}
      >
        <Input placeholder="Adınızı giriniz" />
      </Form.Item>

      <Form.Item
        label="Soyad"
        name="last_name"
        rules={[{ required: true, message: 'Soyad zorunludur.' }]}
      >
        <Input placeholder="Soyadınızı giriniz" />
      </Form.Item>

      <Form.Item
        label="E-Posta"
        name="email"
        rules={[
          { required: true, message: 'E-posta zorunludur.' },
          { type: 'email', message: 'Geçerli bir e-posta adresi giriniz.' }
        ]}
      >
        <Input placeholder="E-posta adresinizi giriniz" />
      </Form.Item>

      <Form.Item
        label="Durum"
        name="status"
        rules={[{ required: true, message: 'Durum seçimi zorunludur.' }]}
      >
        <StatusSelect />
      </Form.Item>

      <Form.Item
        label="Departman (Opsiyonel)"
        name="department_id"
      >
        <DepartmentListSelect departments={departments} placeholder="Departman seçiniz" allowClear />
      </Form.Item>

      <Form.Item
        label="Pozisyon (Opsiyonel)"
        name="jobtitle_id"
      >
        <JobTitleListSelect jobtitles={jobtitles} placeholder="Pozisyon seçiniz" allowClear />
      </Form.Item>

      <Form.Item
        label="Personel Grubu (Opsiyonel)"
        name="usergroup_id"
      >
        <UsergroupSelect usergroups={usergroups} allowClear />
      </Form.Item>

      <Form.Item
        label="Üst Kullanıcı (Opsiyonel)"
        name="parent_user_id"
      >
        <UserSelect users={users.filter(u => u.id !== user.id)} allowClear />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" className="mt-4">
          Kullanıcı Güncelle
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UpdateUserForm;

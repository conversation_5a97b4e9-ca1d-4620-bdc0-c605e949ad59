'use client';

import { But<PERSON>, Form, Input } from 'antd';
import JobTitleListSelect from '@/components/modules/selectbox/JobTitleListSelect';
import DepartmentListSelect from '@/components/modules/selectbox/DepartmentListSelect';
import StatusSelect from '@/components/modules/selectbox/StatusSelect';
import UsergroupSelect from '@/components/modules/selectbox/UsergroupSelect';
import UserSelect from '@/components/modules/selectbox/UserSelect';
import { UserCreateRequest, UserResponse } from '@/types/user.types';
import { DepartmentResponse } from '@/types/department.types';
import { JobtitleResponse } from '@/types/jobtitle.types';
import { UsergroupResponse } from '@/types/usergroup.types';
import { createUser } from '@/app/services/user.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';

interface CreateUserFormProps {
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
  usergroups: UsergroupResponse[];
  users: UserResponse[];
}

const CreateUserForm = ({ departments, jobtitles, usergroups, users }: CreateUserFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const onFinish = async (values: UserCreateRequest) => {
    const res = await createUser(values);
    formHandle(res, () => {
      router.push(ROUTES.USER.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Ad"
        name="first_name"
        rules={[{ required: true, message: 'Ad zorunludur.' }]}
      >
        <Input placeholder="Adınızı giriniz" />
      </Form.Item>

      <Form.Item
        label="Soyad"
        name="last_name"
        rules={[{ required: true, message: 'Soyad zorunludur.' }]}
      >
        <Input placeholder="Soyadınızı giriniz" />
      </Form.Item>

      <Form.Item
        label="E-Posta"
        name="email"
        rules={[
          { required: true, message: 'E-posta zorunludur.' },
          { type: 'email', message: 'Geçerli bir e-posta adresi giriniz.' }
        ]}
      >
        <Input placeholder="E-posta adresinizi giriniz" />
      </Form.Item>

      <Form.Item
        label="Durum"
        name="status"
        initialValue="pending"
        rules={[{ required: true, message: 'Durum seçimi zorunludur.' }]}
      >
        <StatusSelect />
      </Form.Item>

      <Form.Item
        label="Departman (Opsiyonel)"
        name="department_id"
      >
        <DepartmentListSelect departments={departments} placeholder="Departman seçiniz" allowClear />
      </Form.Item>

      <Form.Item
        label="Pozisyon (Opsiyonel)"
        name="jobtitle_id"
      >
        <JobTitleListSelect jobtitles={jobtitles} placeholder="Pozisyon seçiniz" allowClear />
      </Form.Item>

      <Form.Item
        label="Personel Grubu (Opsiyonel)"
        name="usergroup_id"
      >
        <UsergroupSelect usergroups={usergroups} allowClear />
      </Form.Item>

      <Form.Item
        label="Üst Kullanıcı (Opsiyonel)"
        name="parent_user_id"
      >
        <UserSelect users={users} allowClear />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" className="mt-4">
          Kullanıcı Ekle
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CreateUserForm;

import { Button, Form, Input } from 'antd';

const CreateCompanyForm = () => {
  const [form] = Form.useForm();

  return (
    <Form form={form} layout="vertical">
      <Form.Item
        label="Username"
        name="username"
        rules={[
          { required: true, message: 'Username zorunludur' },
          { min: 2, message: 'En az 2 karakter olmalı' },
          { max: 50, message: 'En fazla 50 karakter olabilir' },
        ]}
      >
        <Input placeholder="username" />
      </Form.Item>

      <Form.Item
        label="Email"
        name="email"
        rules={[
          { required: true, message: 'Email zorunludur' },
          { type: 'email', message: 'Geçerli bir email giriniz' },
        ]}
      >
        <Input placeholder="email" />
      </Form.Item>

      <Form.Item>
        <Button>Kaydet</Button>
      </Form.Item>
    </Form>
  );
};

export default CreateCompanyForm;

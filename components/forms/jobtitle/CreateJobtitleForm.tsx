'use client';

import { Button, Form, Input, Switch } from 'antd';
import { useState } from 'react';
import { JobtitleCreateRequest, JobtitleResponse } from '@/types/jobtitle.types';
import { createJobtitle } from '@/app/services/jobtitle.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import JobTitleListSelect from '@/components/modules/selectbox/JobTitleListSelect';

interface CreateJobtitleFormProps {
  jobtitles: JobtitleResponse[];
}

const CreateJobtitleForm = ({ jobtitles }: CreateJobtitleFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();
  const [hasParent, setHasParent] = useState<boolean>(false);

  const onFinish = async (values: JobtitleCreateRequest) => {
    const payload = {
      ...values,
      parent_id: hasParent ? values.parent_id : null,
    };
    const res = await createJobtitle(payload);
    formHandle(res, () => {
      router.push(ROUTES.JOBTITLE.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Pozisyon Adı"
        name="name"
        rules={[{ required: true, message: 'Pozisyon adı zorunludur.' }]}
      >
        <Input placeholder="Pozisyon adını giriniz" />
      </Form.Item>

      <Form.Item label="Açıklama" name="description">
        <Input.TextArea rows={3} placeholder="Pozisyon açıklamasını giriniz (opsiyonel)" />
      </Form.Item>

      <Form.Item label="Üst Pozisyon Var mı?">
        <Switch checked={hasParent} onChange={setHasParent} />
      </Form.Item>

      {hasParent && (
        <Form.Item
          label="Üst Pozisyon"
          name="parent_id"
          rules={[{ required: true, message: 'Üst pozisyon seçimi zorunludur.' }]}
        >
          <JobTitleListSelect
            jobtitles={jobtitles}
            placeholder="Üst pozisyon seçiniz"
            allowClear
          />
        </Form.Item>
      )}

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Kaydet
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CreateJobtitleForm;

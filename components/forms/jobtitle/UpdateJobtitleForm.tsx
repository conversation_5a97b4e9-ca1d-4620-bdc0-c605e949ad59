'use client';

import { Button, Form, Input, Switch } from 'antd';
import { useState, useEffect } from 'react';
import { JobtitleCreateRequest, JobtitleResponse } from '@/types/jobtitle.types';
import { updateJobtitle } from '@/app/services/jobtitle.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import JobTitleListSelect from '@/components/modules/selectbox/JobTitleListSelect';

interface UpdateJobtitleFormProps {
  jobtitle: JobtitleResponse;
  jobtitles: JobtitleResponse[];
}

const UpdateJobtitleForm = ({ jobtitle, jobtitles }: UpdateJobtitleFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const router = useRouter();
  const [hasParent, setHasParent] = useState<boolean>(jobtitle.parent_id !== null);

  useEffect(() => {
    // Form'u jobtitle verileri ile doldur
    form.setFieldsValue({
      name: jobtitle.name,
      description: jobtitle.description,
      parent_id: jobtitle.parent_id,
    });
  }, [jobtitle, form]);

  const onFinish = async (values: JobtitleCreateRequest) => {
    const payload = {
      ...values,
      parent_id: hasParent ? values.parent_id : null,
    };
    
    const res = await updateJobtitle(jobtitle.id, payload);
    formHandle(res, () => {
      router.push(ROUTES.JOBTITLE.INDEX);
    });
  };

  const handleParentChange = (checked: boolean) => {
    setHasParent(checked);
    if (!checked) {
      form.setFieldValue('parent_id', null);
    }
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Pozisyon Adı"
        name="name"
        rules={[{ required: true, message: 'Pozisyon adı zorunludur.' }]}
      >
        <Input placeholder="Pozisyon adını giriniz" />
      </Form.Item>

      <Form.Item label="Açıklama" name="description">
        <Input.TextArea rows={3} placeholder="Pozisyon açıklamasını giriniz (opsiyonel)" />
      </Form.Item>

      <Form.Item label="Üst Pozisyon Var mı?">
        <Switch checked={hasParent} onChange={handleParentChange} />
      </Form.Item>

      {hasParent && (
        <Form.Item
          label="Üst Pozisyon"
          name="parent_id"
          rules={[{ required: true, message: 'Üst pozisyon seçimi zorunludur.' }]}
        >
          <JobTitleListSelect 
            jobtitles={jobtitles} 
            placeholder="Üst pozisyon seçiniz"
            allowClear
          />
        </Form.Item>
      )}

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Güncelle
        </Button>
        <Button 
          className="ml-2" 
          onClick={() => router.push(ROUTES.JOBTITLE.INDEX)}
        >
          İptal
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UpdateJobtitleForm;

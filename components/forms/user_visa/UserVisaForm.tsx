'use client';

import { useEffect } from 'react';
import { Button, DatePicker, Form, Input, Select, Space, Switch } from 'antd';
import { useRouter } from 'next/navigation';
import { useFormHandler } from '@/hooks/useFormHandler';
import { createUserVisa, updateUserVisa } from '@/app/services/user_visa.service';
import { UserVisaCreateRequest, UserVisaFormData, UserVisaResponse } from '@/types/user_visa.types';
import { ROUTES } from '@/lib/routes';
import dayjs from 'dayjs';

interface UserVisaFormProps {
  mode: 'create' | 'update';
  userVisa?: UserVisaResponse;
}

const UserVisaForm = ({ mode, userVisa }: UserVisaFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler<UserVisaResponse>(form);

  useEffect(() => {
    if (mode === 'update' && userVisa) {
      form.setFieldsValue({
        visa_type: userVisa.visa_type,
        entry_type: userVisa.entry_type,
        country: userVisa.country,
        document_number: userVisa.document_number,
        passport_number: userVisa.passport_number,
        start_date: userVisa.start_date ? dayjs(userVisa.start_date) : null,
        end_date: userVisa.end_date ? dayjs(userVisa.end_date) : null,
        duration: userVisa.duration,
        issued_place: userVisa.issued_place,
        issue_date: userVisa.issue_date ? dayjs(userVisa.issue_date) : null,
        is_default: userVisa.is_default,
      });
    }
  }, [form, mode, userVisa]);

  const onFinish = async (values: UserVisaFormData) => {
    console.log('Form values received:', values);

    const payload: UserVisaCreateRequest = {
      visa_type: values.visa_type,
      entry_type: values.entry_type,
      country: values.country,
      document_number: values.document_number,
      passport_number: values.passport_number,
      start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : '',
      end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : '',
      duration: values.duration,
      issued_place: values.issued_place,
      issue_date: values.issue_date ? values.issue_date.format('YYYY-MM-DD') : '',
      is_default: values.is_default || false,
    };

    console.log('Payload to send:', payload);

    let res;
    if (mode === 'create') {
      res = await createUserVisa(payload);
    } else {
      res = await updateUserVisa(userVisa!.id, payload);
    }

    console.log('API Response:', res);
    formHandle(res, () => {
      router.push(ROUTES.PROFILE.VISA.INDEX);
    });
  };

  const visaTypeOptions = [
    { label: 'Turistik', value: 'tourist' },
    { label: 'İş', value: 'business' },
  ];

  const entryTypeOptions = [
    { label: 'Tek', value: 'single' },
    { label: 'Birden Fazla Giriş', value: 'multiple' },
  ];

  const countryOptions = [
    { label: 'Amerika Birleşik Devletleri', value: 'US' },
    { label: 'İngiltere', value: 'GB' },
    { label: 'Almanya', value: 'DE' },
    { label: 'Fransa', value: 'FR' },
    { label: 'İtalya', value: 'IT' },
    { label: 'Japonya', value: 'JP' },
    { label: 'Kanada', value: 'CA' },
    { label: 'Avustralya', value: 'AU' },
    { label: 'Diğer', value: 'OTHER' },
  ];

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Vize Türü"
        name="visa_type"
        rules={[{ required: true, message: 'Vize türü seçiniz.' }]}
      >
        <Select placeholder="Seçiniz" options={visaTypeOptions} />
      </Form.Item>

      <Form.Item
        label="Tek/Birden Fazla Giriş"
        name="entry_type"
        rules={[{ required: true, message: 'Giriş türü seçiniz.' }]}
      >
        <Select placeholder="Seçiniz" options={entryTypeOptions} />
      </Form.Item>

      <Form.Item label="Ülke" name="country" rules={[{ required: true, message: 'Ülke seçiniz.' }]}>
        <Select placeholder="Seçiniz" options={countryOptions} />
      </Form.Item>

      <Form.Item
        label="Belge No"
        name="document_number"
        rules={[{ required: true, message: 'Belge numarası zorunludur.' }]}
      >
        <Input placeholder="Belge numarasını giriniz" />
      </Form.Item>

      <Form.Item
        label="Pasaport Numarası"
        name="passport_number"
        rules={[{ required: true, message: 'Pasaport numarası zorunludur.' }]}
      >
        <Input placeholder="Pasaport numarasını giriniz" />
      </Form.Item>

      <Form.Item
        label="Başlangıç Tarihi"
        name="start_date"
        rules={[{ required: true, message: 'Başlangıç tarihi zorunludur.' }]}
      >
        <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full" />
      </Form.Item>

      <Form.Item
        label="Bitiş Tarihi"
        name="end_date"
        rules={[{ required: true, message: 'Bitiş tarihi zorunludur.' }]}
      >
        <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full" />
      </Form.Item>

      <Form.Item
        label="Kalma Süresi"
        name="duration"
        rules={[{ required: true, message: 'Kalma süresi zorunludur.' }]}
      >
        <Input placeholder="Kalma süresini giriniz (örn: 30 gün)" />
      </Form.Item>

      <Form.Item
        label="Verildiği Yer"
        name="issued_place"
        rules={[{ required: true, message: 'Verildiği yer zorunludur.' }]}
      >
        <Input placeholder="Verildiği yeri giriniz" />
      </Form.Item>

      <Form.Item
        label="Verilme Tarihi"
        name="issue_date"
        rules={[{ required: true, message: 'Verilme tarihi zorunludur.' }]}
      >
        <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full" />
      </Form.Item>

      <Form.Item label="Varsayılan Vize" name="is_default" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {mode === 'create' ? 'Kaydet' : 'Güncelle'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default UserVisaForm;

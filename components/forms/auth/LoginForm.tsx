'use client';

import { Button, Form, Input } from 'antd';
import { loginWithEmail } from '@/app/services/auth.service';
import { useForm } from 'antd/es/form/Form';
import { useFormHandler } from '@/hooks/useFormHandler';
import { LoginFormRequest } from '@/types/auth.types';
import { redirect } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useTransition } from 'react';
import Link from 'next/link';

interface LoginFormProps {
  redirectUrl?: string;
}

const LoginForm = ({ redirectUrl }: LoginFormProps) => {
  const [form] = useForm();
  const { formHandle } = useFormHandler(form);
  const [isPending, startTransition] = useTransition();

  const onFinish = async (values: LoginFormRequest) => {
    startTransition(async () => {
      const res = await loginWithEmail(values);

      formHandle(res, () => {
        if (res.status && res.data.token) {
          const targetUrl = redirectUrl || ROUTES.DASHBOARD;
          redirect(targetUrl);
        }
      });
    });
  };

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <style jsx global>{`
        /* Input.Password placeholder rengi için özel CSS */
        .custom-password-input input::placeholder,
        .custom-password-input .ant-input-affix-wrapper input::placeholder {
          color: rgb(156, 163, 175) !important; /* gray-400 */
          opacity: 1 !important;
        }

        .custom-password-input input,
        .custom-password-input .ant-input-affix-wrapper input {
          color: white !important;
          background: transparent !important;
        }
      `}</style>

      <div className="flex w-full max-w-lg flex-col space-y-6">
        {/* Header Section */}
        <div className="flex flex-col items-center space-y-3 text-center">
          <h1 className="text-3xl font-bold text-white">
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
              Giriş Yap
            </span>
          </h1>
          <p className="text-base text-gray-300">Hesabınıza giriş yaparak devam edin</p>
        </div>

        {/* Form Container */}
        <div className="rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-8 backdrop-blur-sm">
          <Form
            disabled={isPending}
            form={form}
            layout="vertical"
            className="flex flex-col"
            onFinish={onFinish}
            autoComplete="off"
          >
            <Form.Item
              label={<span className="font-medium text-white">Email</span>}
              name="email"
              rules={[
                { required: true, message: 'Email zorunludur.' },
                { type: 'email', message: 'Geçerli bir email giriniz.' },
              ]}
              className="!mb-6"
            >
              <Input
                size="large"
                placeholder="Email adresinizi girin"
                className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
              />
            </Form.Item>

            <Form.Item
              label={<span className="font-medium text-white">Şifre</span>}
              name="password"
              rules={[{ required: true, message: 'Şifre zorunludur.' }]}
              className="!mb-6"
            >
              <Input.Password
                size="large"
                placeholder="Şifrenizi girin"
                className="custom-password-input !rounded-xl"
                style={{
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  borderColor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                }}
              />
            </Form.Item>

            <Form.Item className="!mb-0">
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                loading={isPending}
                className="!h-12 w-full transform !rounded-xl !border-none !bg-gradient-to-r !from-blue-600 !to-purple-600 !text-base !font-semibold !shadow-lg transition-all duration-300 hover:scale-[1.02] hover:!from-blue-700 hover:!to-purple-700 hover:!shadow-xl"
              >
                <span className="flex items-center justify-center gap-2">🔑 Giriş Yap</span>
              </Button>
            </Form.Item>
          </Form>

          {/* Additional Info */}
          <div className="mt-6 flex flex-col items-center space-y-2">
            <p className="text-center text-sm text-gray-400">
              <Link
                href="/"
                className="font-medium text-purple-400 transition-colors hover:text-purple-300"
              >
                ← Anasayfaya dön
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;

'use client';

import { Button, Form, Input, Select } from 'antd';
import { OrganizationResponse } from '@/types/organization.type';
import { useState } from 'react';
import { enmUserOrganization } from '@/enums/UserOrganization';
import { useFormHandler } from '@/hooks/useFormHandler';
import { registerWithOrganization } from '@/app/services/auth.service';
import { RegisterFormRequest } from '@/types/auth.types';
import { redirect } from 'next/navigation';
import Link from 'next/link';

interface RegisterFormProps {
  organizations: OrganizationResponse[];
}

const RegisterForm = ({ organizations }: RegisterFormProps) => {
  const [form] = Form.useForm();
  const { formHandle } = useFormHandler(form);
  const [showAgency, setShowAgency] = useState<boolean>(false);

  const handleOrganizationTypeChange = (organizationType: number) => {
    setShowAgency(organizationType === enmUserOrganization.COMPANY);
  };

  const onFinish = async (values: RegisterFormRequest) => {
    const res = await registerWithOrganization(values);
    formHandle(res, () => {
      redirect('/login');
    });
  };

  return (
    <div className="flex min-h-screen w-full items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <div className="flex w-full max-w-2xl flex-col space-y-6">
        {/* Header Section */}
        <div className="flex flex-col items-center space-y-3 text-center">
          <h1 className="text-3xl font-bold text-white">
            <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
              Hesap Oluşturun
            </span>
          </h1>
          <p className="text-base text-gray-300">Dijital seyahat yönetimi platformumuza katılın</p>
        </div>

        {/* Form Container */}
        <div className="rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-8 backdrop-blur-sm">
          <Form form={form} layout="vertical" className="flex flex-col" onFinish={onFinish}>
            <Form.Item
              label={<span className="font-medium text-white">İşletme Tipi</span>}
              name="organization_type"
              rules={[{ required: true, message: 'Tip zorunludur.' }]}
              className="!mb-6"
            >
              <Select
                size="large"
                onChange={handleOrganizationTypeChange}
                placeholder="İşletme tipi seçin"
                className="custom-select"
                dropdownStyle={{
                  background: 'rgba(30, 41, 59, 0.95)',
                  backdropFilter: 'blur(12px)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                }}
                options={[
                  { value: 1, label: '🏢 Acente' },
                  { value: 2, label: '🏭 Firma' },
                ]}
              />
            </Form.Item>

            {showAgency && (
              <Form.Item
                label={<span className="font-medium text-white">Acente Seçiniz</span>}
                name="parent_id"
                rules={[{ required: true, message: 'Firma iseniz üst acente seçimi zorunludur.' }]}
                className="!mb-6"
              >
                <Select
                  size="large"
                  className="custom-select"
                  dropdownStyle={{
                    background: 'rgba(30, 41, 59, 0.95)',
                    backdropFilter: 'blur(12px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                  }}
                  filterOption={(inputValue, option) => {
                    const label = option?.label?.toString().toLowerCase() || '';
                    return label.includes(inputValue.toLowerCase());
                  }}
                  allowClear
                  showSearch
                  placeholder="Acente seçin..."
                  options={organizations.map((organization) => ({
                    value: organization.id,
                    label: organization.name,
                  }))}
                />
              </Form.Item>
            )}

            <Form.Item
              label={<span className="font-medium text-white">Firma Ünvanı</span>}
              name="organization_name"
              rules={[{ required: true, message: 'Firma zorunludur.' }]}
              className="!mb-6"
            >
              <Input
                size="large"
                className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                placeholder="Firma ünvanınızı girin"
              />
            </Form.Item>

            <div className="mb-6 flex flex-col space-y-2">
              <Form.Item
                label={<span className="font-medium text-white">Kurumsal E-posta</span>}
                name="email"
                rules={[
                  { required: true, message: 'E-posta zorunludur.' },
                  { type: 'email', message: 'Geçerli bir e-posta adresi girin.' },
                ]}
                className="!mb-0"
              >
                <Input
                  type="email"
                  size="large"
                  className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                  placeholder="<EMAIL>"
                />
              </Form.Item>
              <span className="text-xs text-gray-400">
                Lütfen firma isminizi taşıyan kurumsal e-posta adresinizi kullanın
              </span>
            </div>

            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
              <Form.Item
                label={<span className="font-medium text-white">İsim</span>}
                name="first_name"
                rules={[{ required: true, message: 'İsim zorunludur.' }]}
                className="!mb-0"
              >
                <Input
                  size="large"
                  className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                  placeholder="İsminiz"
                />
              </Form.Item>

              <Form.Item
                label={<span className="font-medium text-white">Soyisim</span>}
                name="last_name"
                rules={[{ required: true, message: 'Soyisim zorunludur.' }]}
                className="!mb-0"
              >
                <Input
                  size="large"
                  className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                  placeholder="Soyisminiz"
                />
              </Form.Item>
            </div>

            <div className="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
              <Form.Item
                label={<span className="font-medium text-white">Şifre</span>}
                name="password"
                rules={[{ required: true, message: 'Şifre zorunludur.' }, { min: 8 }, { max: 50 }]}
                className="!mb-0"
              >
                <Input.Password
                  size="large"
                  className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                  placeholder="Şifreniz"
                />
              </Form.Item>

              <Form.Item
                label={<span className="font-medium text-white">Şifre Tekrar</span>}
                name="password_confirmation"
                dependencies={['password']}
                rules={[
                  { required: true, message: 'Şifre tekrarı zorunludur.' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('Şifreler uyuşmuyor.'));
                    },
                  }),
                ]}
                className="!mb-0"
              >
                <Input.Password
                  size="large"
                  className="!rounded-xl !border-white/20 !bg-white/10 !text-white placeholder:!text-gray-400 hover:!border-white/30 focus:!border-blue-400/50"
                  placeholder="Şifrenizi tekrar girin"
                />
              </Form.Item>
            </div>

            <Form.Item className="!mb-0">
              <Button
                type="primary"
                htmlType="submit"
                size="large"
                className="!h-12 w-full transform !rounded-xl !border-none !bg-gradient-to-r !from-blue-600 !to-purple-600 !text-base !font-semibold !shadow-lg transition-all duration-300 hover:scale-[1.02] hover:!from-blue-700 hover:!to-purple-700 hover:!shadow-xl"
              >
                <span className="flex items-center justify-center gap-2">🚀 Hesap Oluştur</span>
              </Button>
            </Form.Item>
          </Form>

          {/* Additional Info */}
          <div className="mt-6 flex flex-col items-center space-y-2">
            <p className="text-center text-sm text-gray-400">
              Zaten hesabınız var mı?{' '}
              <a
                href="/login"
                className="font-medium text-blue-400 transition-colors hover:text-blue-300"
              >
                Giriş yapın
              </a>
            </p>
            <p className="text-center text-sm text-gray-400">
              <Link
                href="/"
                className="font-medium text-purple-400 transition-colors hover:text-purple-300"
              >
                ← Anasayfaya dön
              </Link>
            </p>
          </div>
        </div>

        {/* Information Alert */}
        <div className="mb-6 rounded-xl border border-blue-400/20 bg-gradient-to-br from-blue-500/10 to-purple-500/10 p-4 backdrop-blur-sm">
          <div className="flex items-start gap-3">
            <div className="mt-0.5 flex-shrink-0 text-xl text-blue-400">ℹ️</div>
            <div className="flex flex-col space-y-3">
              <h3 className="text-base font-semibold text-white">Kayıt Koşulları</h3>
              <div className="flex flex-col space-y-2 text-sm text-gray-300">
                <p className="flex flex-col space-y-1">
                  <span className="font-medium text-blue-400">• E-posta Koşulu:</span>
                  <span>
                    Sadece firma isminizi taşıyan kurumsal e-posta adresleri ile kayıt
                    olabilirsiniz.
                  </span>
                </p>
                <p className="ml-4 text-xs text-gray-400">Örnek: <EMAIL></p>
                <p className="flex flex-col space-y-1">
                  <span className="font-medium text-purple-400">• Onay Süreci:</span>
                  <span>
                    Kayıt işleminizin tamamlanmasının ardından, sistem yöneticilerimiz tarafından
                    hesabınız incelenecektir.
                  </span>
                </p>
                <p className="flex flex-col space-y-1">
                  <span className="font-medium text-green-400">• Aktivasyon:</span>
                  <span>
                    Onay sürecinin tamamlanması halinde, e-posta adresinize tek kullanımlık
                    aktivasyon kodu gönderilecektir.
                  </span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegisterForm;

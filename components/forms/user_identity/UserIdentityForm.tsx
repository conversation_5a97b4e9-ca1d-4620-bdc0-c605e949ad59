'use client';

import { useEffect } from 'react';
import { Button, Form, Input, Space, Switch } from 'antd';
import { useRouter } from 'next/navigation';
import { useFormHandler } from '@/hooks/useFormHandler';
import { createUserIdentity, updateUserIdentity } from '@/app/services/user_identity.service';
import { UserIdentityCreateRequest, UserIdentityResponse } from '@/types/user_identity.types';
import { ROUTES } from '@/lib/routes';
import CountrySelect from '@/components/modules/selectbox/CountrySelect';

interface UserIdentityFormProps {
  mode: 'create' | 'update';
  userIdentity?: UserIdentityResponse;
}

const UserIdentityForm = ({ mode, userIdentity }: UserIdentityFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler<UserIdentityResponse>(form);

  useEffect(() => {
    if (mode === 'update' && userIdentity) {
      form.setFieldsValue({
        nationality_code: userIdentity.nationality_code,
        identity_number: userIdentity.identity_number,
        is_default: userIdentity.is_default,
      });
    }
  }, [form, mode, userIdentity]);

  const onFinish = async (values: UserIdentityCreateRequest) => {
    const payload: UserIdentityCreateRequest = {
      nationality_code: values.nationality_code,
      identity_number: values.identity_number,
      is_default: values.is_default || false,
    };

    let res;
    if (mode === 'create') {
      res = await createUserIdentity(payload);
    } else {
      res = await updateUserIdentity(userIdentity!.id, payload);
    }

    formHandle(res, () => {
      router.push(ROUTES.PROFILE.IDENTITY.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <Form.Item
        label="Kimlik Numarası"
        name="identity_number"
        rules={[{ required: true, message: 'Kimlik numarası zorunludur.' }]}
      >
        <Input placeholder="Kimlik numarasını giriniz" />
      </Form.Item>

      <Form.Item
        label="Uyruk"
        name="nationality_code"
        rules={[{ required: true, message: 'Uyruk zorunludur.' }]}
      >
        <CountrySelect />
      </Form.Item>

      <Form.Item label="Varsayılan Kimlik" name="is_default" valuePropName="checked">
        <Switch />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit">
            {mode === 'create' ? 'Kaydet' : 'Güncelle'}
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default UserIdentityForm;

'use client';

import {useEffect} from 'react';
import {Button, DatePicker, Form, Input, Space, Switch} from 'antd';
import {useRouter} from 'next/navigation';
import {useFormHandler} from '@/hooks/useFormHandler';
import {createUserPassport, updateUserPassport} from '@/app/services/user_passport.service';
import {UserPassportCreateRequest, UserPassportFormData, UserPassportResponse,} from '@/types/user_passport.types';
import {ROUTES} from '@/lib/routes';
import dayjs from 'dayjs';
import CountrySelect from "@/components/modules/selectbox/CountrySelect";

interface UserPassportFormProps {
    mode: 'create' | 'update';
    userPassport?: UserPassportResponse;
}

const UserPassportForm = ({mode, userPassport}: UserPassportFormProps) => {
    const [form] = Form.useForm();
    const router = useRouter();
    const {formHandle} = useFormHandler<UserPassportResponse>(form);

    useEffect(() => {
        if (mode === 'update' && userPassport) {
            form.setFieldsValue({
                nationality: userPassport.nationality,
                passport_number: userPassport.passport_number,
                issue_date: userPassport.issue_date ? dayjs(userPassport.issue_date) : null,
                expiry_date: userPassport.expiry_date ? dayjs(userPassport.expiry_date) : null,
                is_default: userPassport.is_default,
            });
        }
    }, [form, mode, userPassport]);

    const onFinish = async (values: UserPassportFormData) => {

        const payload: UserPassportCreateRequest = {
            nationality: values.nationality,
            passport_number: values.passport_number,
            issue_date: values.issue_date ? values.issue_date.format('YYYY-MM-DD') : '',
            expiry_date: values.expiry_date ? values.expiry_date.format('YYYY-MM-DD') : '',
            is_default: values.is_default || false,
        };


        let res;
        if (mode === 'create') {
            res = await createUserPassport(payload);
        } else {
            res = await updateUserPassport(userPassport!.id, payload);
        }

        formHandle(res, () => {
            router.push(ROUTES.PROFILE.PASSPORT.INDEX);
        });
    };

    return (
        <Form form={form} layout="vertical" onFinish={onFinish}>
            <Form.Item
                label="Uyruk"
                name="nationality"
                rules={[{required: true, message: 'Uyruk seçiniz.'}]}
            >
                <CountrySelect/>
            </Form.Item>


            <Form.Item
                label="Pasaport Numarası"
                name="passport_number"
                rules={[{required: true, message: 'Pasaport numarası zorunludur.'}]}
            >
                <Input placeholder="Pasaport numarasını giriniz"/>
            </Form.Item>

            <Form.Item
                label="Verilme Tarihi"
                name="issue_date"
                rules={[{required: true, message: 'Verilme tarihi zorunludur.'}]}
            >
                <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full"/>
            </Form.Item>

            <Form.Item
                label="Geçerlilik Süresi"
                name="expiry_date"
                rules={[{required: true, message: 'Geçerlilik süresi zorunludur.'}]}
            >
                <DatePicker placeholder="gg/aa/yyyy" format="DD/MM/YYYY" className="w-full"/>
            </Form.Item>

            <Form.Item label="Varsayılan Pasaport" name="is_default" valuePropName="checked">
                <Switch/>
            </Form.Item>

            <Form.Item>
                <Space>
                    <Button type="primary" htmlType="submit">
                        {mode === 'create' ? 'Kaydet' : 'Güncelle'}
                    </Button>
                </Space>
            </Form.Item>
        </Form>
    );
};

export default UserPassportForm;

'use client';

import { But<PERSON>, Form } from 'antd';
import GuestFormFields from './GuestFormFields';
import { GuestCreateRequest, GuestFormData } from '@/types/guest.types';
import { createGuest } from '@/app/services/guest.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import dayjs from 'dayjs';

const CreateGuestForm = () => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  const onFinish = async (values: GuestFormData) => {
    const payload: GuestCreateRequest = {
      ...values,
      birth_date: values.birth_date ? dayjs(values.birth_date).format('YYYY-MM-DD') : '',
      passport_valid_date: values.passport_valid_date
        ? dayjs(values.passport_valid_date).format('YYYY-MM-DD')
        : undefined,
    };

    const res = await createGuest(payload);
    formHandle(res, () => {
      router.push(ROUTES.GUEST.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <GuestFormFields />

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Kişi Ekle
        </Button>
        <Button className="ml-2" onClick={() => router.push(ROUTES.GUEST.INDEX)}>
          İptal
        </Button>
      </Form.Item>
    </Form>
  );
};

export default CreateGuestForm;

'use client';

import { <PERSON><PERSON>, <PERSON> } from 'antd';
import GuestFormFields from './GuestFormFields';
import { GuestCreateRequest, GuestFormData, GuestResponse } from '@/types/guest.types';
import { updateGuest } from '@/app/services/guest.service';
import { useFormHandler } from '@/hooks/useFormHandler';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { useEffect } from 'react';
import dayjs from 'dayjs';

interface UpdateGuestFormProps {
  guest: GuestResponse;
}

const UpdateGuestForm = ({ guest }: UpdateGuestFormProps) => {
  const [form] = Form.useForm();
  const router = useRouter();
  const { formHandle } = useFormHandler(form);

  useEffect(() => {
    form.setFieldsValue({
      gender: guest.gender,
      first_name: guest.first_name,
      last_name: guest.last_name,
      email: guest.email,
      phone_code: guest.phone_code,
      phone_number: guest.phone_number,
      birth_date: guest.birth_date ? dayjs(guest.birth_date) : null,
      nationality: guest.nationality,
      identity_number: guest.identity_number,
      passport_number: guest.passport_number,
      passport_valid_date: guest.passport_valid_date ? dayjs(guest.passport_valid_date) : null,
      loyalty_airline: guest.loyalty_airline,
      loyalty_card_number: guest.loyalty_card_number,
    });
  }, [form, guest]);

  const onFinish = async (values: GuestFormData) => {
    const payload: GuestCreateRequest = {
      ...values,
      birth_date: values.birth_date ? dayjs(values.birth_date).format('YYYY-MM-DD') : '',
      passport_valid_date: values.passport_valid_date
        ? dayjs(values.passport_valid_date).format('YYYY-MM-DD')
        : undefined,
    };

    const res = await updateGuest(guest.id, payload);
    formHandle(res, () => {
      router.push(ROUTES.GUEST.INDEX);
    });
  };

  return (
    <Form form={form} layout="vertical" onFinish={onFinish}>
      <GuestFormFields />

      <Form.Item>
        <Button type="primary" htmlType="submit">
          Güncelle
        </Button>
        <Button className="ml-2" onClick={() => router.push(ROUTES.GUEST.INDEX)}>
          İptal
        </Button>
      </Form.Item>
    </Form>
  );
};

export default UpdateGuestForm;

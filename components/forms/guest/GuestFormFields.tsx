'use client';

import { DatePicker, Form, Input } from 'antd';
import GenderSelect from '@/components/modules/selectbox/GenderSelect';
import CountrySelect from '@/components/modules/selectbox/CountrySelect';
import PhoneCodeSelect from '@/components/modules/selectbox/PhoneCodeSelect';
import AirlineSelect from '@/components/modules/selectbox/AirlineSelect';
import dayjs from 'dayjs';

const GuestFormFields = () => {
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <Form.Item
        label="Cinsiyet"
        name="gender"
        rules={[{ required: true, message: 'Cinsiyet zorunludur' }]}
      >
        <GenderSelect />
      </Form.Item>

      <Form.Item
        label="Ad"
        name="first_name"
        rules={[
          { required: true, message: 'Ad zorunludur' },
          { min: 2, message: 'Ad en az 2 karakter olmalıdır' },
          { max: 255, message: 'Ad en fazla 255 karakter olabilir' },
        ]}
      >
        <Input placeholder="Ad" />
      </Form.Item>

      <Form.Item
        label="Soyad"
        name="last_name"
        rules={[
          { required: true, message: 'Soyad zorunludur' },
          { min: 2, message: 'Soyad en az 2 karakter olmalıdır' },
          { max: 255, message: 'Soyad en fazla 255 karakter olabilir' },
        ]}
      >
        <Input placeholder="Soyad" />
      </Form.Item>

      <Form.Item
        label="E-Posta Adresi"
        name="email"
        rules={[
          { required: true, message: 'E-posta zorunludur' },
          { type: 'email', message: 'Geçerli bir e-posta giriniz' },
          { max: 255, message: 'E-posta en fazla 255 karakter olabilir' },
        ]}
      >
        <Input placeholder="E-posta" />
      </Form.Item>

      <Form.Item
        label="Telefon Kodu"
        name="phone_code"
        rules={[{ required: true, message: 'Telefon kodu zorunludur' }]}
      >
        <PhoneCodeSelect />
      </Form.Item>

      <Form.Item
        label="Telefon Numarası"
        name="phone_number"
        rules={[
          { required: true, message: 'Telefon numarası zorunludur' },
          { max: 20, message: 'Telefon numarası en fazla 20 karakter olabilir' },
        ]}
      >
        <Input placeholder="Telefon Numarası" />
      </Form.Item>

      <Form.Item
        label="Doğum Tarihi"
        name="birth_date"
        rules={[{ required: true, message: 'Doğum tarihi zorunludur' }]}
      >
        <DatePicker format="DD/MM/YYYY" style={{ width: '100%' }} placeholder="gg/aa/yyyy" />
      </Form.Item>

      <Form.Item
        label="Uyruk"
        name="nationality"
        rules={[{ required: true, message: 'Uyruk zorunludur' }]}
      >
        <CountrySelect placeholder="Uyruk seçiniz" />
      </Form.Item>

      <Form.Item
        label="Kimlik Numarası"
        name="identity_number"
        rules={[{ max: 50, message: 'Kimlik numarası en fazla 50 karakter olabilir' }]}
      >
        <Input placeholder="Kimlik Numarası" />
      </Form.Item>

      <Form.Item
        label="Pasaport Numarası"
        name="passport_number"
        rules={[{ max: 50, message: 'Pasaport numarası en fazla 50 karakter olabilir' }]}
      >
        <Input placeholder="Pasaport Numarası" />
      </Form.Item>

      <Form.Item
        label="Geçerlilik Tarihi"
        name="passport_valid_date"
        rules={[
          {
            validator: (_, value) => {
              if (!value) return Promise.resolve();
              const today = dayjs().startOf('day');
              const selectedDate = dayjs(value).startOf('day');
              if (selectedDate.isAfter(today)) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('Geçerlilik tarihi bugünden sonra olmalıdır'));
            },
          },
        ]}
      >
        <DatePicker
          format="DD/MM/YYYY"
          className="w-full"
          placeholder="gg/aa/yyyy"
          disabledDate={(current) => current && current < dayjs().startOf('day')}
        />
      </Form.Item>

      <Form.Item
        label="Sadakat Kartı Havayolu"
        name="loyalty_airline"
        rules={[{ max: 50, message: 'Havayolu kodu en fazla 50 karakter olabilir' }]}
      >
        <AirlineSelect />
      </Form.Item>

      <Form.Item
        label="Kart Numarası"
        name="loyalty_card_number"
        rules={[{ max: 50, message: 'Kart numarası en fazla 50 karakter olabilir' }]}
      >
        <Input placeholder="Kart Numarası" />
      </Form.Item>
    </div>
  );
};

export default GuestFormFields;

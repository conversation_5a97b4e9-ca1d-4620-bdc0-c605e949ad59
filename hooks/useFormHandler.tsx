import { toast } from 'sonner';
import { FormInstance } from 'antd';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export function useFormHandler<T = unknown>(form: FormInstance) {
  const formHandle = (res: ApiResponseResource<T> | ApiResponseCollection<T>, onSuccess?: () => void): void => {
    if (!res.status) {
      const errors = res.errors ?? {};
      const shownMessages = new Set<string>();

      // Tüm hata mesajlarını göster (tekrar etmeden)
      Object.values(errors).forEach((messages) => {
        (Array.isArray(messages) ? messages : [String(messages)]).forEach((msg) => {
          if (!shownMessages.has(msg)) {
            toast.error(msg, { closeButton: true });
            shownMessages.add(msg);
          }
        });
      });

      // <PERSON> yo<PERSON>, genel mesaj göster
      if (Object.keys(errors).length === 0 && res.message) {
        toast.error(res.message, { closeButton: true });
      }

      // AntD için alan hatalarını set et
      const fieldErrors = Object.entries(errors).map(([fieldName, messages]) => ({
        name: fieldName,
        errors: Array.isArray(messages) ? messages : [String(messages)],
      }));

      form.setFields(fieldErrors);
      return;
    }

    // Başarılıysa formu resetle ve callback çalıştır
    form.resetFields();
    onSuccess?.();
  };

  return { formHandle };
}

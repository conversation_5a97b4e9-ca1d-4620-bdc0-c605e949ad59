'use client';

import {Button} from 'antd';
import Link from 'next/link';
import {useEffect, useState} from 'react';
import Image from 'next/image';

const Home = () => {
    const [isVisible, setIsVisible] = useState(false);

    useEffect(() => {
        setIsVisible(true);
    }, []);

    const features = [
        {
            title: 'Acenteler için',
            description:
                'Otel, uçak bileti ve araç kiralama gibi hizmetlerinizi firmalara entegre edin. Dijital kanalınızla yeni müşterilere ulaşın.',
            gradient: 'from-blue-500 to-cyan-500',
            icon: '🏢',
        },
        {
            title: 'Firmalar için',
            description:
                'Kendi iş ortağınızı seçin, tüm seyahat ve lojistik süreçlerinizi tek bir platformdan yönetin. Operasyonel verimliliğinizi artırın.',
            gradient: 'from-green-500 to-emerald-500',
            icon: '🏭',
        },
        {
            title: '<PERSON><PERSON><PERSON>',
            description:
                '<PERSON><PERSON><PERSON><PERSON>, esnek ve kullanıcı dostu yapımız sayesinde iş süreçlerinizi kesintisiz ve hızlı bir şekilde entegre edin.',
            gradient: 'from-purple-500 to-pink-500',
            icon: '⚡',
        },
    ];

    const stats = [
        {number: '500+', label: 'Aktif İş Ortağı'},
        {number: '1M+', label: 'İşlem Hacmi'},
        {number: '99.9%', label: 'Uptime Garantisi'},
        {number: '24/7', label: 'Destek Hizmeti'},
    ];

    return (
        <div className="min-h-screen w-full bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col">
            {/* Top Navigation - Flex Header */}
            <header className="w-full bg-black/20 backdrop-blur-sm flex-shrink-0">
                <div className="w-full max-w-6xl mx-auto px-4 py-6">
                    <div className="flex justify-between items-center">
                        <div className="flex items-center">
                            <Image
                                src="https://app-biziclick.tourvisio.com/assets/img/logo-biziclick-light-bg.svg"
                                alt="Logo"
                                width={160}
                                height={60}
                                className="h-8 w-auto lg:h-10"
                                priority
                            />
                        </div>
                        <nav className="flex gap-3 lg:gap-4">
                            <Link href="/login">
                                <Button
                                    size="large"
                                    className="!border-white/30 !text-white hover:!border-white/60 hover:!text-white !bg-transparent"
                                >
                                    Giriş Yap
                                </Button>
                            </Link>
                            <Link href="/register">
                                <Button
                                    size="large"
                                    className="!border-white/30 !text-white hover:!border-white/60 hover:!text-white !bg-purple-950"
                                >
                                    Kayıt Ol
                                </Button>
                            </Link>
                        </nav>
                    </div>
                </div>
            </header>

            {/* Ana İçerik - Flex-grow ile genişleyen alan */}
            <main className="flex-grow flex items-center justify-center py-8">
                <div className="w-full max-w-6xl mx-auto px-4">
                    <div className="flex flex-col items-center justify-center space-y-12">

                        {/* Hero Section */}
                        <section className="text-center space-y-6 max-w-5xl">
                            <div className="inline-flex items-center rounded-full border border-blue-500/20 bg-gradient-to-r from-blue-500/10 to-purple-500/10 px-4 py-2 text-sm font-medium text-blue-400 backdrop-blur-sm">
                                🚀 Yeni Nesil İş Ortaklıkları
                            </div>

                            <h1 className="text-3xl font-bold tracking-tight text-white sm:text-4xl md:text-5xl lg:text-6xl">
                                <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-blue-400 bg-clip-text text-transparent">
                                    Acente ve Firmalara Özel
                                </span>
                                <br/>
                                <span className="text-white">Dijital Seyahat Yönetimi</span>
                            </h1>

                            <p className="mx-auto max-w-4xl text-base leading-relaxed text-gray-300 sm:text-lg lg:text-xl xl:text-2xl">
                                Seyahat, konaklama ve taşıma gibi operasyonel hizmetleri dijitalleştirerek; firmaların
                                seçtikleri acentelerle entegre bir şekilde çalışmasını sağlayan yeni nesil bir altyapı
                                sunuyoruz. Tüm süreçlerinizi merkezi bir platformdan yönetin, zaman ve kaynak
                                tasarrufu sağlayın.
                            </p>
                        </section>

                        {/* Features Grid */}
                        <section className="w-full max-w-6xl">
                            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {features.map((feature, index) => (
                                    <div
                                        key={index}
                                        className={`group relative rounded-2xl border border-white/10 bg-gradient-to-br from-white/10 to-white/5 p-6 backdrop-blur-sm transition-all duration-500 hover:scale-[1.02] hover:border-white/20 hover:bg-white/10 ${
                                            isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'
                                        }`}
                                        style={{transitionDelay: `${index * 100}ms`}}
                                    >
                                        <div className="relative z-10 space-y-4">
                                            <div className="flex items-center gap-3">
                                                <span className="text-2xl lg:text-3xl">{feature.icon}</span>
                                                <h3
                                                    className={`bg-gradient-to-r text-lg font-bold lg:text-xl ${feature.gradient} bg-clip-text text-transparent`}
                                                >
                                                    {feature.title}
                                                </h3>
                                            </div>
                                            <p className="text-sm leading-relaxed text-gray-300 lg:text-base">{feature.description}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </section>

                        {/* Stats Section */}
                        <section className="w-full max-w-6xl">
                            <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                                {stats.map((stat, index) => (
                                    <div
                                        key={index}
                                        className="space-y-2 rounded-xl border border-white/10 bg-white/5 p-4 text-center backdrop-blur-sm transition-all duration-300 hover:bg-white/10 hover:border-white/20"
                                    >
                                        <div className="text-xl font-bold text-white sm:text-2xl lg:text-3xl">{stat.number}</div>
                                        <div className="text-xs text-gray-400 sm:text-sm lg:text-base">{stat.label}</div>
                                    </div>
                                ))}
                            </div>
                        </section>
                    </div>
                </div>
            </main>

            {/* Footer - İsteğe bağlı */}
            <footer className="flex-shrink-0 w-full border-t border-white/10 bg-black/20 backdrop-blur-sm">
                <div className="w-full max-w-6xl mx-auto px-4 py-6">
                    <div className="flex flex-col items-center justify-center space-y-2 md:flex-row md:justify-between md:space-y-0">
                        <p className="text-sm text-gray-400">
                            © 2024 BiziClick. Tüm hakları saklıdır.
                        </p>
                        <div className="flex space-x-6 text-sm text-gray-400">
                            <a href="#" className="hover:text-white transition-colors">Gizlilik</a>
                            <a href="#" className="hover:text-white transition-colors">Kullanım Koşulları</a>
                            <a href="#" className="hover:text-white transition-colors">İletişim</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    );
};

export default Home;
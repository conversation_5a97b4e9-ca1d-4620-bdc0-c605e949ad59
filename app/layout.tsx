import '@ant-design/v5-patch-for-react-19';
import { ConfigProvider } from 'antd';
import type { Metadata } from 'next';
import './globals.css';
import { Poppins } from 'next/font/google';
import { ReactNode } from 'react';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import { locale, theme } from '@/lib/theme';
import { Toaster } from 'sonner';

const poppins = Poppins({
  weight: '400',
  subsets: ['latin'],
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'BiziClick',
  description: 'Seyahat acenteleri için bilet satış platformu',
};

type Props = {
  children: ReactNode;
};

export default function RootLayout({ children }: Props) {
  return (
    <html lang="tr">
      <body
        className={`${poppins.className} text-ssm min-h-screen antialiased`}
        suppressHydrationWarning
      >
        <ConfigProvider theme={theme} locale={locale}>
          <Toaster position="top-center" richColors />
          <AntdRegistry>{children}</AntdRegistry>
        </ConfigProvider>
      </body>
    </html>
  );
}

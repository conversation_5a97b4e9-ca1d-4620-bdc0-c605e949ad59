import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateUserForm from '@/components/forms/user/CreateUserForm';
import { getDepartments } from '@/app/services/department.service';
import { getJobtitles } from '@/app/services/jobtitle.service';
import { getUsergroups } from '@/app/services/usergroup.service';
import { getUsers } from '@/app/services/user.service';

const UserCreatePage = async () => {
  const [departments, jobtitles, usergroups, users] = await Promise.all([
    getDepartments(),
    getJobtitles(),
    getUsergroups(),
    getUsers(),
  ]);

  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Personel Ekle"
        links={[{ title: 'Personel Listesi', link: ROUTES.USER.INDEX }, { title: 'Personel Ekle' }]}
      />
      <KBCard className="m-4 p-4">
        <CreateUserForm
          departments={departments.status ? departments.data.items : []}
          jobtitles={jobtitles.status ? jobtitles.data.items : []}
          usergroups={usergroups.status ? usergroups.data.items : []}
          users={users.status ? users.data.items : []}
        />
      </KBCard>
    </>
  );
};

export default UserCreatePage;

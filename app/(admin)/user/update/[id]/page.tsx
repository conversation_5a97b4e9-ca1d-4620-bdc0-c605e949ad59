import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import {ROUTES} from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateUserForm from '@/components/forms/user/UpdateUserForm';
import {getUserById, getUsers} from '@/app/services/user.service';
import {notFound} from 'next/navigation';
import {getDepartments} from "@/app/services/department.service";
import {getJobtitles} from "@/app/services/jobtitle.service";
import {getUsergroups} from "@/app/services/usergroup.service";

interface UserUpdatePageProps {
    params: Promise<{
        id: number;
    }>;
}

const UserUpdatePage = async ({ params }: UserUpdatePageProps) => {
  const { id } = await params;

  if (isNaN(id)) {
    notFound();
  }

  const [userResponse, departments, jobtitles, usergroups, users] = await Promise.all([
    getUserById(id),
    getDepartments(),
    getJobtitles(),
    getUsergroups(),
    getUsers(),
  ]);

    console.log(userResponse);

  if (!userResponse.status || !userResponse.data) {
    notFound();
  }

  const user = userResponse.data;

  return (
    <>
      <HeaderBreadcrumb
        content="Personel Güncelle"
        links={[
          { title: 'Personel Listesi', link: ROUTES.USER.INDEX },
          { title: 'Personel Güncelle' },
        ]}
      />

      <KBCard className="m-4 p-4">
        <UpdateUserForm
          user={user}
          departments={departments.status ? departments.data.items : []}
          jobtitles={jobtitles.status ? jobtitles.data.items : []}
          usergroups={usergroups.status ? usergroups.data.items : []}
          users={users.status ? users.data.items : []}
        />
      </KBCard>
    </>
  );
};

export default UserUpdatePage;

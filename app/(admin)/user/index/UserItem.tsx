'use client';

import { <PERSON><PERSON>, <PERSON>confirm, Space, Tag } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { FaUser } from 'react-icons/fa';
import { ROUTES } from '@/lib/routes';
import { UserResponse } from '@/types/user.types';
import { deleteUser } from '@/app/services/user.service';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useState } from 'react';

interface UserItemProps {
  user: UserResponse;
}

export const UserItem = ({ user }: UserItemProps) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    const res = await deleteUser(id);

    if (res.status) {
      toast.success(res.message);
      router.refresh();
    } else {
      toast.error(res.message);
    }

    setIsDeleting(false);
  };

  const handleUpdate = () => {
    router.push(ROUTES.USER.UPDATE(user.id));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'green';
      case 'inactive':
        return 'red';
      case 'pending':
        return 'orange';
      default:
        return 'gray';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'inactive':
        return 'Pasif';
      case 'pending':
        return 'Beklemede';
      default:
        return status;
    }
  };

  return (
    <li className="ml-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <FaUser className="text-sm text-blue-950" />
            <span>
              {user.first_name} {user.last_name}
            </span>
            <Tag color={getStatusColor(user.status)} className="text-xs">
              {getStatusText(user.status)}
            </Tag>
          </div>
          <span className="text-xs text-blue-400">{user.email}</span>
        </div>

        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={handleUpdate}
            className="text-xs"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Kullanıcıyı Sil"
            description={`"${user.first_name} ${user.last_name}" kullanıcısını silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(user.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={isDeleting}
              disabled={isDeleting}
              className="text-xs"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      </div>
    </li>
  );
};

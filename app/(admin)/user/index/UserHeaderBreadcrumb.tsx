import {Tooltip, Toolt<PERSON>Content, TooltipProvider, TooltipTrigger} from '@/components/ui/tooltip';
import Link from 'next/link';
import {ROUTES} from '@/lib/routes';
import {Button} from 'antd';
import {LiaFileExportSolid, LiaFileImportSolid} from 'react-icons/lia';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';

const UserHeaderBreadcrumb = () => {
  return (
    <HeaderBreadcrumb
      content={
        <div className="flex items-center gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href={ROUTES.USER.CREATE}>
                  <Button type="default" icon={<LiaFileImportSolid className="text-2xl" />} />
                </Link>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Personel Listesi İçe Aktar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Link href={ROUTES.USER.CREATE}>
                  <Button type="default" icon={<LiaFileExportSolid className="text-2xl" />} />
                </Link>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Personel Listesi Dışa aktar</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Link href={ROUTES.USER.CREATE}>
            <Button type="primary">Personel Ekle</Button>
          </Link>
        </div>
      }
      links={[{ title: 'Personel Listesi' }]}
    />
  );
};

export default UserHeaderBreadcrumb;

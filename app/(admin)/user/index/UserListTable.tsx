'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Select, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUser, getUsers } from '@/app/services/user.service';
import { UserResponse } from '@/types/user.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import KBAvatar from '@/components/ui/custom/KbAvatar';
import Link from 'next/link';

const UserListTable = () => {
  const [users, setUsers] = useState<UserResponse[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await getUsers();
      if (response.status) {
        setUsers(response.data.items);
        setFilteredUsers(response.data.items);
      }
    } catch {
      toast.error('Veriler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadUsers = async () => {
      await fetchUsers();
    };

    void loadUsers();
  }, []);

  const filterAndSearchUsers = (searchValue: string, filterValue: string) => {
    let filtered = users;

    if (filterValue !== 'all') {
      filtered = filtered.filter((user) => user.status === filterValue);
    }

    if (searchValue) {
      filtered = filtered.filter(
        (user) =>
          user.first_name.toLowerCase().includes(searchValue.toLowerCase()) ||
          user.last_name.toLowerCase().includes(searchValue.toLowerCase()) ||
          user.email.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUsers(filtered);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
    filterAndSearchUsers(searchText, value);
  };

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUsers(value, selectedFilter);
  };

  const filterOptions = [
    { value: 'all', label: 'Tümü' },
    { value: 'active', label: 'Aktif' },
    { value: 'inactive', label: 'Pasif' },
    { value: 'pending', label: 'Beklemede' },
  ];

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUser(id);

    if (res.status) {
      toast.success(res.message);
      console.log(await fetchUsers());
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.USER.UPDATE(id));
  };

  const getStatusLabel = (status: string) => {
    const statusLabels = {
      active: {
        label: 'Aktif',
        className: 'bg-green-100 text-green-800 border-green-200',
      },
      inactive: {
        label: 'Pasif',
        className: 'bg-red-100 text-red-800 border-red-200',
      },
      pending: {
        label: 'Beklemede',
        className: 'bg-orange-100 text-orange-800 border-orange-200',
      },
    };
    return (
      statusLabels[status as keyof typeof statusLabels] || {
        label: status,
        className: 'bg-gray-100 text-gray-800 border-gray-200',
      }
    );
  };

  const columns: ColumnsType<UserResponse> = [
    {
      title: 'Avatar',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <KBAvatar
          src={`https://ui-avatars.com/api/?name=${record.first_name}+${record.last_name}&background=random`}
          fallback={`${record.first_name.charAt(0)}${record.last_name.charAt(0)}`}
          size={40}
        />
      ),
    },
    {
      title: 'Ad Soyad',
      dataIndex: 'name',
      key: 'name',
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <div>
          <Link href={ROUTES.PROFILE.SHOW(record.id)} className="font-medium">
            {record.first_name} {record.last_name}
          </Link>
          <div className="text-xs text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: 'Durum',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      className: 'px-6 py-4',
      render: (status: string) => {
        const statusInfo = getStatusLabel(status);
        return (
          <span
            className={`inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium ${statusInfo.className}`}
          >
            {statusInfo.label}
          </span>
        );
      },
    },
    {
      title: 'Departman',
      dataIndex: 'department',
      key: 'department',
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <span className="text-sm">{record.department?.name || '-'}</span>
      ),
    },
    {
      title: 'Pozisyon',
      dataIndex: 'job_title',
      key: 'job_title',
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <span className="text-sm">{record.job_title?.name || '-'}</span>
      ),
    },
    {
      title: 'Grup',
      dataIndex: 'user_group',
      key: 'user_group',
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <span className="text-sm">{record.user_group?.name || '-'}</span>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Personeli Sil"
            description={`"${record.first_name} ${record.last_name}" personelini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-500 transition-all duration-200 hover:bg-red-50 hover:text-red-600"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <FilterOutlined className="mr-1 text-gray-500" />
                Durum
              </label>
              <Select
                placeholder="Durum seçiniz"
                value={selectedFilter}
                onChange={handleFilterChange}
                options={filterOptions}
                className="w-full"
                size="middle"
                onClear={() => {
                  setSelectedFilter('all');
                  filterAndSearchUsers(searchText, 'all');
                }}
              />
            </div>

            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Ad, soyad veya e-posta ile ara..."
                value={searchText}
                onChange={handleSearchChange}
                prefix={<SearchOutlined className="text-gray-400" />}
                size="middle"
                allowClear
                className=""
              />
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-800">{filteredUsers.length}</div>
              <div className="text-xs text-gray-500">Gösterilen</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{users.length}</div>
              <div className="text-xs text-gray-500">Toplam</div>
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          size="large"
          className="modern-table -mt-5 [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
        />
      </KBCard>
    </div>
  );
};

export default UserListTable;

import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserListTable from '@/app/(admin)/user/index/UserListTable';

const UserIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.USER.CREATE}>
            <Button type="primary">Personel Ekle</Button>
          </Link>
        }
        links={[{ title: 'Personel Listesi' }]}
      />
      <div className="m-4">
        <UserListTable />
      </div>
    </>
  );
};

export default UserIndexPage;

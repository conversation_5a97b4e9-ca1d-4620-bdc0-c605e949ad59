import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateBranchForm from '@/components/forms/branch/UpdateBranchForm';
import { getBranchById } from '@/app/services/branch.service';
import { notFound } from 'next/navigation';

interface BranchUpdatePageProps {
  params: Promise<{ id: string }>;
}

const BranchUpdatePage = async ({ params }: BranchUpdatePageProps) => {
  const { id } = await params;
  const branchId = parseInt(id);

  if (isNaN(branchId)) {
    notFound();
  }

  const branchResponse = await getBranchById(branchId);

  if (!branchResponse.status || !branchResponse.data) {
    notFound();
  }

  const branch = branchResponse.data;

  return (
    <>
      <HeaderBreadcrumb
        content="Şube Güncelle"
        links={[{ title: '<PERSON><PERSON><PERSON>', link: ROUTES.BRANCH.INDEX }, { title: '<PERSON><PERSON> Güncelle' }]}
      />
      <KBCard className="m-4 p-4">
        <UpdateBranchForm branch={branch} />
      </KBCard>
    </>
  );
};

export default BranchUpdatePage;

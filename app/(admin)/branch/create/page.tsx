import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import CreateBranchForm from '@/components/forms/branch/CreateBranchForm';

const BranchCreatePage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Ş<PERSON>"
        links={[{ title: '<PERSON><PERSON><PERSON>', link: ROUTES.BRANCH.INDEX }, { title: '<PERSON><PERSON>' }]}
      />

      <div className="p-4">
        <CreateBranchForm />
      </div>
    </>
  );
};

export default BranchCreatePage;

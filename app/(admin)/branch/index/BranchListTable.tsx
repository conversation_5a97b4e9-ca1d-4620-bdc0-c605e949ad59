'use client';

import React, { useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Select, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteBranch, getBranches } from '@/app/services/branch.service';
import { BranchResponse } from '@/types/branch.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaRegBuilding } from 'react-icons/fa';

const BranchListTable = () => {
  const [branches, setBranches] = useState<BranchResponse[]>([]);
  const [filteredBranches, setFilteredBranches] = useState<BranchResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const router = useRouter();

  const fetchBranches = async () => {
    setLoading(true);
    const res = await getBranches();

    if (res.status && res.data?.items) {
      setBranches(res.data.items);
      setFilteredBranches(res.data.items);
    } else {
      setBranches([]);
      setFilteredBranches([]);
    }

    setLoading(false);
  };

  useEffect(() => {
    void fetchBranches();
  }, []);

  const filterAndSearchBranches = (searchValue: string, filterValue: string) => {
    let filtered = branches;

    if (filterValue === 'with_tax') {
      filtered = filtered.filter((branch) => branch.tax_number && branch.tax_number.trim() !== '');
    } else if (filterValue === 'without_tax') {
      filtered = filtered.filter((branch) => !branch.tax_number || branch.tax_number.trim() === '');
    }

    if (searchValue) {
      filtered = filtered.filter(
        (branch) =>
          branch.address_name.toLowerCase().includes(searchValue.toLowerCase()) ||
          branch.address_1.toLowerCase().includes(searchValue.toLowerCase()) ||
          (branch.city && branch.city.toLowerCase().includes(searchValue.toLowerCase())) ||
          (branch.tax_office &&
            branch.tax_office.toLowerCase().includes(searchValue.toLowerCase())),
      );
    }

    setFilteredBranches(filtered);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
    filterAndSearchBranches(searchText, value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchBranches(value, selectedFilter);
  };

  const filterOptions = [
    { value: 'all', label: 'Tümü' },
    { value: 'with_tax', label: 'Vergi Numarası Olan' },
    { value: 'without_tax', label: 'Vergi Numarası Olmayan' },
  ];

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteBranch(id);

    if (res.status) {
      toast.success(res.message);
      await fetchBranches();
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.BRANCH.UPDATE(id));
  };

  const columns: ColumnsType<BranchResponse> = [
    {
      title: 'Şube Bilgileri',
      dataIndex: 'address_name',
      key: 'address_name',
      className: 'px-6 py-4',
      render: (address_name: string, record: BranchResponse) => (
        <div className="flex items-center gap-2">
          <FaRegBuilding className="text-blue-600" />
          <div>
            <div className="font-medium">{address_name}</div>
            <div className="text-xs text-gray-500">{record.address_1}</div>
            {record.city && <div className="text-xs text-gray-400">{record.city}</div>}
          </div>
        </div>
      ),
    },
    {
      title: 'Adres Türü',
      dataIndex: 'address_type',
      key: 'address_type',
      width: 150,
      className: 'px-6 py-4',
      render: (address_type: string) => (
        <span className="inline-flex items-center rounded-full border border-blue-200 bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
          {address_type}
        </span>
      ),
    },
    {
      title: 'Vergi Bilgileri',
      key: 'tax_info',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: BranchResponse) => (
        <div>
          {record.tax_number && <div className="text-sm font-medium">{record.tax_number}</div>}
          {record.tax_office && <div className="text-xs text-gray-500">{record.tax_office}</div>}
          {!record.tax_number && !record.tax_office && (
            <span className="text-xs text-gray-400">Vergi bilgisi yok</span>
          )}
        </div>
      ),
    },
    {
      title: 'İletişim',
      key: 'contact',
      width: 150,
      className: 'px-6 py-4',
      render: (_value: string, record: BranchResponse) => (
        <div>
          {record.phone_number && (
            <div className="text-sm">
              {record.country_code_phone} {record.phone_number}
            </div>
          )}
          {record.fax_number && (
            <div className="text-xs text-gray-500">
              Faks: {record.country_code_fax} {record.fax_number}
            </div>
          )}
          {!record.phone_number && !record.fax_number && (
            <span className="text-xs text-gray-400">İletişim bilgisi yok</span>
          )}
        </div>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: BranchResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Şubeyi Sil"
            description={`"${record.address_name}" şubesini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <FilterOutlined className="mr-1 text-gray-500" />
                Şube Tipi
              </label>
              <Select
                placeholder="Tip seçiniz"
                value={selectedFilter}
                onChange={handleFilterChange}
                options={filterOptions}
                className="w-full"
                size="middle"
                allowClear
                onClear={() => {
                  setSelectedFilter('all');
                  filterAndSearchBranches(searchText, 'all');
                }}
              />
            </div>

            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Şube adı, adres veya şehir ile ara..."
                value={searchText}
                onChange={handleSearchChange}
                prefix={<SearchOutlined className="text-gray-400" />}
                size="middle"
                allowClear
                className=""
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredBranches}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          scroll={{ x: 'max-content' }}
          size="large"
          className="modern-table -mt-6 [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
        />
      </KBCard>
    </div>
  );
};

export default BranchListTable;

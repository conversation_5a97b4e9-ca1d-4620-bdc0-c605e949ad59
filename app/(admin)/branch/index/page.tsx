import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import BranchListTable from '@/app/(admin)/branch/index/BranchListTable';

const BranchIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.BRANCH.CREATE}>
            <Button type="primary"><PERSON><PERSON>kle</Button>
          </Link>
        }
        links={[{ title: '<PERSON><PERSON><PERSON>' }]}
      />
      <div className="m-4">
        <BranchListTable />
      </div>
    </>
  );
};

export default BranchIndexPage;

'use client';

import React, { useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Select, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteGuest, getGuests } from '@/app/services/guest.service';
import { GuestResponse } from '@/types/guest.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import KBAvatar from '@/components/ui/custom/KbAvatar';

const GuestListTable = () => {
  const [guests, setGuests] = useState<GuestResponse[]>([]);
  const [filteredGuests, setFilteredGuests] = useState<GuestResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const router = useRouter();

  const fetchGuests = async () => {
    setLoading(true);
    try {
      const response = await getGuests();
      if (response.status && response.data) {
        setGuests(response.data.items);
        setFilteredGuests(response.data.items);
      }
    } catch (error) {
      console.error('Error fetching guests:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    void fetchGuests();
  }, []);

  useEffect(() => {
    let filtered = guests;

    // Arama filtresi
    if (searchText) {
      filtered = filtered.filter(
        (guest) =>
          guest.first_name.toLowerCase().includes(searchText.toLowerCase()) ||
          guest.last_name.toLowerCase().includes(searchText.toLowerCase()) ||
          guest.email.toLowerCase().includes(searchText.toLowerCase()) ||
          guest.phone_number.toLowerCase().includes(searchText.toLowerCase()),
      );
    }

    // Cinsiyet filtresi
    if (selectedFilter && selectedFilter !== 'all') {
      filtered = filtered.filter((guest) => guest.gender === selectedFilter);
    }

    setFilteredGuests(filtered);
  }, [guests, searchText, selectedFilter]);

  const handleEdit = (id: number) => {
    router.push(ROUTES.GUEST.UPDATE(id));
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    try {
      const response = await deleteGuest(id);
      if (response.status) {
        toast.success('Misafir başarıyla silindi');
        await fetchGuests();
      }
    } catch (error) {
      console.error('Error deleting guest:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
  };

  const filterOptions = [
    { value: 'all', label: 'Tüm Cinsiyetler' },
    { value: 'male', label: 'Erkek' },
    { value: 'female', label: 'Kadın' },
    { value: 'other', label: 'Diğer' },
  ];

  const columns: ColumnsType<GuestResponse> = [
    {
      title: 'Avatar',
      dataIndex: 'avatar',
      key: 'avatar',
      width: 80,
      className: 'px-6 py-4',
      render: (_value: string, record: GuestResponse) => (
        <KBAvatar
          src={`https://ui-avatars.com/api/?name=${record.first_name}+${record.last_name}&background=random`}
          fallback={`${record.first_name.charAt(0)}${record.last_name.charAt(0)}`}
          size={40}
        />
      ),
    },
    {
      title: 'Ad Soyad',
      dataIndex: 'name',
      key: 'name',
      className: 'px-6 py-4',
      render: (_value: string, record: GuestResponse) => (
        <div>
          <div className="font-medium">
            {record.first_name} {record.last_name}
          </div>
          <div className="text-xs text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: 'Telefon',
      dataIndex: 'phone_number',
      key: 'phone_number',
      className: 'px-6 py-4',
      render: (_value: string, record: GuestResponse) => (
        <div>
          <div className="font-medium">
            +{record.phone_code} {record.phone_number}
          </div>
        </div>
      ),
    },
    {
      title: 'Cinsiyet',
      dataIndex: 'gender',
      key: 'gender',
      className: 'px-6 py-4',
      render: (gender: string) => {
        const genderMap = {
          male: 'Erkek',
          female: 'Kadın',
          other: 'Diğer',
        };
        return genderMap[gender as keyof typeof genderMap] || gender;
      },
    },
    {
      title: 'Uyruk',
      dataIndex: 'nationality',
      key: 'nationality',
      className: 'px-6 py-4',
    },
    {
      title: 'Doğum Tarihi',
      dataIndex: 'birth_date',
      key: 'birth_date',
      className: 'px-6 py-4',
      render: (date: string) => {
        if (!date) return '-';
        return new Date(date).toLocaleDateString('tr-TR');
      },
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: GuestResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-500 transition-all duration-200 hover:bg-blue-50 hover:text-blue-600"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Misafiri Sil"
            description={`"${record.first_name} ${record.last_name}" misafirini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-500 transition-all duration-200 hover:bg-red-50 hover:text-red-600"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <FilterOutlined className="mr-1 text-gray-500" />
                Cinsiyet Filtresi
              </label>
              <Select
                placeholder="Tip seçiniz"
                value={selectedFilter}
                onChange={handleFilterChange}
                options={filterOptions}
                className="w-full"
                size="middle"
                allowClear
                onClear={() => {
                  setSelectedFilter('all');
                }}
              />
            </div>

            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Ad, soyad, e-posta veya telefon ile ara..."
                value={searchText}
                onChange={handleSearchChange}
                prefix={<SearchOutlined className="text-gray-400" />}
                size="middle"
                allowClear
                className=""
              />
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-800">{filteredGuests.length}</div>
              <div className="text-xs text-gray-500">Gösterilen</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{guests.length}</div>
              <div className="text-xs text-gray-500">Toplam</div>
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredGuests}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          scroll={{ x: 'max-content' }}
          size="middle"
          className="modern-table -mt-5 [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
        />
      </KBCard>
    </div>
  );
};

export default GuestListTable;

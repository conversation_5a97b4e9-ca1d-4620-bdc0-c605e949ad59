import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import GuestListTable from '@/app/(admin)/guest/index/GuestListTable';

const GuestIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.GUEST.CREATE}>
            <Button type="primary">Misa<PERSON><PERSON> Ekle</Button>
          </Link>
        }
        links={[{ title: 'Misafirler' }]}
      />
      <div className="m-4">
        <GuestListTable />
      </div>
    </>
  );
};

export default GuestIndexPage;

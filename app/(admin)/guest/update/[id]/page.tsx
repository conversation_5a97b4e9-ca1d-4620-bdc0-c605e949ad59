import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateGuestForm from '@/components/forms/guest/UpdateGuestForm';
import { getGuestById } from '@/app/services/guest.service';
import { notFound } from 'next/navigation';

interface GuestUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const GuestUpdatePage = async ({ params }: GuestUpdatePageProps) => {
  const { id } = await params;
  const guestId = parseInt(id);

  if (isNaN(guestId)) {
    notFound();
  }

  const response = await getGuestById(guestId);

  if (!response.status || !response.data) {
    notFound();
  }

  const guest = response.data;

  return (
    <>
      <HeaderBreadcrumb
        content="Misafir Güncelle"
        links={[{ title: 'Misafirler', link: ROUTES.GUEST.INDEX }, { title: '<PERSON><PERSON><PERSON><PERSON>' }]}
      />
      <KBCard className="m-4 p-4">
        <UpdateGuestForm guest={guest} />
      </KBCard>
    </>
  );
};

export default GuestUpdatePage;

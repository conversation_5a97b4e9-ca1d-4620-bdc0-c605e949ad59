import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateGuestForm from '@/components/forms/guest/CreateGuestForm';

const GuestCreatePage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content="<PERSON><PERSON>"
        links={[{ title: 'Misafirler', link: ROUTES.GUEST.INDEX }, { title: '<PERSON><PERSON><PERSON><PERSON>' }]}
      />
      <KBCard className="m-4 p-4">
        <CreateGuestForm />
      </KBCard>
    </>
  );
};

export default GuestCreatePage;

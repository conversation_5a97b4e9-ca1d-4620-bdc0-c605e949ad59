import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserPassportForm from '@/components/forms/user_passport/UserPassportForm';
import { getUserPassportById } from '@/app/services/user_passport.service';
import { notFound } from 'next/navigation';

interface UserPassportUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const UserPassportUpdatePage = async ({ params }: UserPassportUpdatePageProps) => {
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id);

  if (isNaN(id)) {
    notFound();
  }

  const userPassportResponse = await getUserPassportById(id);

  if (!userPassportResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Pasaport Bilgisi Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Pasaport Bilgileri', link: ROUTES.PROFILE.PASSPORT.INDEX },
          { title: 'Pasaport Bilgisi Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserPassportForm mode="update" userPassport={userPassportResponse.data} />
      </KBCard>
    </>
  );
};

export default UserPassportUpdatePage;

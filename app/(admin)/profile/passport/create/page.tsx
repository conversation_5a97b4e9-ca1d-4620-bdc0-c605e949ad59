import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserPassportForm from '@/components/forms/user_passport/UserPassportForm';

const UserPassportCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Pasaport Bilgisi Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Pasaport Bilgileri', link: ROUTES.PROFILE.PASSPORT.INDEX },
          { title: 'Pasaport Bilgisi Ekle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserPassportForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserPassportCreatePage;

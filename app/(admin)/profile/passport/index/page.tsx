import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserPassportListTable from '@/app/(admin)/profile/passport/index/UserPassportListTable';
import { getUserPassports } from '@/app/services/user_passport.service';

const UserPassportIndexPage = async () => {
  const userPassportsResponse = await getUserPassports();
  const userPassports = userPassportsResponse.status ? userPassportsResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.PASSPORT.CREATE}>
            <Button type="primary">Pasaport Bilgisi Ekle</Button>
          </Link>
        }
        links={[{ title: 'Profil', link: ROUTES.PROFILE.INDEX }, { title: 'Pasaport Bilgileri' }]}
      />
      <div className="m-4">
        <UserPassportListTable initialData={userPassports} />
      </div>
    </>
  );
};

export default UserPassportIndexPage;

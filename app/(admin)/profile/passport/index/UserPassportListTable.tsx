'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserPassport } from '@/app/services/user_passport.service';
import { UserPassportResponse } from '@/types/user_passport.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaPassport } from 'react-icons/fa';
import dayjs from 'dayjs';

interface UserPassportListTableProps {
  initialData: UserPassportResponse[];
}

const UserPassportListTable = ({ initialData }: UserPassportListTableProps) => {
  const [userPassports, setUserPassports] = useState<UserPassportResponse[]>(initialData);
  const [filteredUserPassports, setFilteredUserPassports] =
    useState<UserPassportResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserPassports = (searchValue: string) => {
    let filtered = userPassports;

    if (searchValue) {
      filtered = filtered.filter(
        (passport) =>
          passport.passport_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          passport.nationality.toLowerCase().includes(searchValue.toLowerCase()) ||
          passport.country.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserPassports(filtered);
  };

  useEffect(() => {
    setUserPassports(initialData);
    setFilteredUserPassports(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserPassports(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserPassport(id);

    if (res.status) {
      toast.success(res.message);
      const updatedPassports = userPassports.filter((passport) => passport.id !== id);
      setUserPassports(updatedPassports);
      setFilteredUserPassports(updatedPassports);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.PASSPORT.UPDATE(id));
  };

  const isExpired = (expiryDate: string) => {
    return dayjs(expiryDate).isBefore(dayjs());
  };

  const isExpiringSoon = (expiryDate: string) => {
    return dayjs(expiryDate).isBefore(dayjs().add(6, 'month')) && !isExpired(expiryDate);
  };

  const columns: ColumnsType<UserPassportResponse> = [
    {
      title: 'Pasaport Bilgisi',
      dataIndex: 'passport_number',
      key: 'passport_number',
      className: 'px-6 py-4',
      render: (passportNumber: string, record: UserPassportResponse) => (
        <div className="flex items-center gap-2">
          <FaPassport className="text-blue-600" />
          <div>
            <div className="font-medium">{passportNumber}</div>
            <div className="text-xs text-gray-500">
              {record.nationality} - {record.country}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Verilme Tarihi',
      dataIndex: 'issue_date',
      key: 'issue_date',
      width: 120,
      className: 'px-6 py-4',
      render: (date: string) => <span className="text-sm">{dayjs(date).format('DD/MM/YYYY')}</span>,
    },
    {
      title: 'Geçerlilik',
      dataIndex: 'expiry_date',
      key: 'expiry_date',
      width: 150,
      className: 'px-6 py-4',
      render: (date: string) => {
        const expired = isExpired(date);
        const expiringSoon = isExpiringSoon(date);

        return (
          <div>
            <div className="text-sm">{dayjs(date).format('DD/MM/YYYY')}</div>
            {expired && <Tag color="red">Süresi Dolmuş</Tag>}
            {expiringSoon && <Tag color="orange">Yakında Dolacak</Tag>}
            {!expired && !expiringSoon && <Tag color="green">Geçerli</Tag>}
          </div>
        );
      },
    },
    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'gold' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserPassportResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Pasaport Bilgisini Sil"
            description={`"${record.passport_number}" pasaport bilgisini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Pasaport numarası, uyruk veya ülke ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserPassports}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserPassportListTable;

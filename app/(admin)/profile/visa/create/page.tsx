import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserVisaForm from '@/components/forms/user_visa/UserVisaForm';

const UserVisaCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Vize Bilgisi Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Vize Bilgileri', link: ROUTES.PROFILE.VISA.INDEX },
          { title: 'Vize Bilgisi <PERSON>kle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserVisaForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserVisaCreatePage;

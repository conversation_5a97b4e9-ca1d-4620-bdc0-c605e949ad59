import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserVisaForm from '@/components/forms/user_visa/UserVisaForm';
import { getUserVisaById } from '@/app/services/user_visa.service';
import { notFound } from 'next/navigation';

interface UserVisaUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const UserVisaUpdatePage = async ({ params }: UserVisaUpdatePageProps) => {
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id);

  if (isNaN(id)) {
    notFound();
  }

  const userVisaResponse = await getUserVisaById(id);

  if (!userVisaResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Vize Bilgisi Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Vize Bilgileri', link: ROUTES.PROFILE.VISA.INDEX },
          { title: 'Vize Bilgisi Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserVisaForm mode="update" userVisa={userVisaResponse.data} />
      </KBCard>
    </>
  );
};

export default UserVisaUpdatePage;

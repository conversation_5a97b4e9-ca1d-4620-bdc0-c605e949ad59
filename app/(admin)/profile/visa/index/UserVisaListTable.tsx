'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserVisa } from '@/app/services/user_visa.service';
import { UserVisaResponse } from '@/types/user_visa.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaPassport } from 'react-icons/fa';
import dayjs from 'dayjs';

interface UserVisaListTableProps {
  initialData: UserVisaResponse[];
}

const UserVisaListTable = ({ initialData }: UserVisaListTableProps) => {
  const [userVisas, setUserVisas] = useState<UserVisaResponse[]>(initialData);
  const [filteredUserVisas, setFilteredUserVisas] = useState<UserVisaResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserVisas = (searchValue: string) => {
    let filtered = userVisas;

    if (searchValue) {
      filtered = filtered.filter(
        (visa) =>
          visa.document_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          visa.passport_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          visa.country.toLowerCase().includes(searchValue.toLowerCase()) ||
          visa.issued_place.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserVisas(filtered);
  };

  useEffect(() => {
    setUserVisas(initialData);
    setFilteredUserVisas(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserVisas(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserVisa(id);

    if (res.status) {
      toast.success(res.message);
      const updatedVisas = userVisas.filter((visa) => visa.id !== id);
      setUserVisas(updatedVisas);
      setFilteredUserVisas(updatedVisas);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.VISA.UPDATE(id));
  };

  const isExpired = (endDate: string) => {
    return dayjs(endDate).isBefore(dayjs());
  };

  const isExpiringSoon = (endDate: string) => {
    return dayjs(endDate).isBefore(dayjs().add(1, 'month')) && !isExpired(endDate);
  };

  const getVisaTypeLabel = (type: string) => {
    switch (type) {
      case 'tourist':
        return 'Turistik';
      case 'business':
        return 'İş';
      default:
        return 'Diğer';
    }
  };

  const getEntryTypeLabel = (type: string) => {
    switch (type) {
      case 'single':
        return 'Tek';
      case 'multiple':
        return 'Çoklu';
      default:
        return 'Diğer';
    }
  };

  const columns: ColumnsType<UserVisaResponse> = [
    {
      title: 'Vize Bilgisi',
      dataIndex: 'document_number',
      key: 'document_number',
      className: 'px-6 py-4',
      render: (documentNumber: string, record: UserVisaResponse) => (
        <div className="flex items-center gap-2">
          <FaPassport className="text-green-600" />
          <div>
            <div className="font-medium">{documentNumber}</div>
            <div className="text-xs text-gray-500">
              {record.country} - {getVisaTypeLabel(record.visa_type)}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: 'Tür',
      dataIndex: 'visa_type',
      key: 'visa_type',
      width: 100,
      className: 'px-6 py-4',
      render: (type: string) => (
        <Tag color={type === 'tourist' ? 'blue' : 'orange'}>{getVisaTypeLabel(type)}</Tag>
      ),
    },
    {
      title: 'Giriş',
      dataIndex: 'entry_type',
      key: 'entry_type',
      width: 100,
      className: 'px-6 py-4',
      render: (type: string) => (
        <Tag color={type === 'single' ? 'default' : 'green'}>{getEntryTypeLabel(type)}</Tag>
      ),
    },
    {
      title: 'Geçerlilik',
      dataIndex: 'end_date',
      key: 'end_date',
      width: 150,
      className: 'px-6 py-4',
      render: (date: string) => {
        const expired = isExpired(date);
        const expiringSoon = isExpiringSoon(date);

        return (
          <div>
            <div className="text-sm">{dayjs(date).format('DD/MM/YYYY')}</div>
            {expired && <Tag color="red">Süresi Dolmuş</Tag>}
            {expiringSoon && <Tag color="orange">Yakında Dolacak</Tag>}
            {!expired && !expiringSoon && <Tag color="green">Geçerli</Tag>}
          </div>
        );
      },
    },
    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'gold' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserVisaResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Vize Bilgisini Sil"
            description={`"${record.document_number}" vize bilgisini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Belge no, pasaport no, ülke veya verildiği yer ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserVisas}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserVisaListTable;

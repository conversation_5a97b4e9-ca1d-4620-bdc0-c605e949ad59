import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserVisaListTable from '@/app/(admin)/profile/visa/index/UserVisaListTable';
import { getUserVisas } from '@/app/services/user_visa.service';

const UserVisaIndexPage = async () => {
  const userVisasResponse = await getUserVisas();
  const userVisas = userVisasResponse.status ? userVisasResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.VISA.CREATE}>
            <Button type="primary">Vize Bilgisi Ekle</Button>
          </Link>
        }
        links={[{ title: 'Profil', link: ROUTES.PROFILE.INDEX }, { title: 'Vize Bilgileri' }]}
      />
      <div className="m-4">
        <UserVisaListTable initialData={userVisas} />
      </div>
    </>
  );
};

export default UserVisaIndexPage;

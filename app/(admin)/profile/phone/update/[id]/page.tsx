import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserPhoneForm from '@/components/forms/user_phone/UserPhoneForm';
import { getUserPhoneById } from '@/app/services/user_phone.service';
import { notFound } from 'next/navigation';

interface UserPhoneUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const UserPhoneUpdatePage = async ({ params }: UserPhoneUpdatePageProps) => {
  const resolvedParams = await params;
  const id = parseInt(resolvedParams.id);

  if (isNaN(id)) {
    notFound();
  }

  const userPhoneResponse = await getUserPhoneById(id);

  if (!userPhoneResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Telefon Bilgisi Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Telefon Bilgileri', link: ROUTES.PROFILE.PHONE.INDEX },
          { title: 'Telefon Bilgisi Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserPhoneForm mode="update" userPhone={userPhoneResponse.data} />
      </KBCard>
    </>
  );
};

export default UserPhoneUpdatePage;

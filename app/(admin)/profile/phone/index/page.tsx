import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserPhoneListTable from '@/app/(admin)/profile/phone/index/UserPhoneListTable';
import { getUserPhones } from '@/app/services/user_phone.service';

const UserPhoneIndexPage = async () => {
  const userPhonesResponse = await getUserPhones();
  const userPhones = userPhonesResponse.status ? userPhonesResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.PHONE.CREATE}>
            <Button type="primary">Telefon Bilgisi Ekle</Button>
          </Link>
        }
        links={[{ title: 'Profil', link: ROUTES.PROFILE.INDEX }, { title: 'Telefon Bilgileri' }]}
      />
      <div className="m-4">
        <UserPhoneListTable initialData={userPhones} />
      </div>
    </>
  );
};

export default UserPhoneIndexPage;

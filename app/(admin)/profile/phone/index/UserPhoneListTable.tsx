'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserPhone } from '@/app/services/user_phone.service';
import { UserPhoneResponse } from '@/types/user_phone.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaExclamationTriangle, FaMobile, FaPhone } from 'react-icons/fa';

interface UserPhoneListTableProps {
  initialData: UserPhoneResponse[];
}

const UserPhoneListTable = ({ initialData }: UserPhoneListTableProps) => {
  const [userPhones, setUserPhones] = useState<UserPhoneResponse[]>(initialData);
  const [filteredUserPhones, setFilteredUserPhones] = useState<UserPhoneResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserPhones = (searchValue: string) => {
    let filtered = userPhones;

    if (searchValue) {
      filtered = filtered.filter(
        (phone) =>
          phone.phone_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          phone.country_code.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserPhones(filtered);
  };

  useEffect(() => {
    setUserPhones(initialData);
    setFilteredUserPhones(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserPhones(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserPhone(id);

    if (res.status) {
      toast.success(res.message);
      const updatedPhones = userPhones.filter((phone) => phone.id !== id);
      setUserPhones(updatedPhones);
      setFilteredUserPhones(updatedPhones);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.PHONE.UPDATE(id));
  };

  const getPhoneTypeIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <FaPhone className="text-blue-600" />;
      case 'mobile':
        return <FaMobile className="text-green-600" />;
      case 'emergency':
        return <FaExclamationTriangle className="text-red-600" />;
      default:
        return <FaPhone className="text-gray-600" />;
    }
  };

  const getPhoneTypeLabel = (type: string) => {
    switch (type) {
      case 'home':
        return 'Ev';
      case 'mobile':
        return 'Cep';
      case 'emergency':
        return 'Acil';
      default:
        return 'Diğer';
    }
  };

  const columns: ColumnsType<UserPhoneResponse> = [
    {
      title: 'Telefon Bilgisi',
      dataIndex: 'phone_number',
      key: 'phone_number',
      className: 'px-6 py-4',
      render: (phoneNumber: string, record: UserPhoneResponse) => (
        <div className="flex items-center gap-2">
          {getPhoneTypeIcon(record.type)}
          <div>
            <div className="font-medium">
              {record.country_code} {phoneNumber}
            </div>
            <div className="text-xs text-gray-500">{getPhoneTypeLabel(record.type)}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Tür',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      className: 'px-6 py-4',
      render: (type: string) => (
        <Tag color={type === 'home' ? 'blue' : type === 'mobile' ? 'green' : 'red'}>
          {getPhoneTypeLabel(type)}
        </Tag>
      ),
    },
    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'gold' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserPhoneResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Telefon Bilgisini Sil"
            description={`"${record.country_code} ${record.phone_number}" telefon bilgisini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Telefon numarası ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserPhones}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserPhoneListTable;

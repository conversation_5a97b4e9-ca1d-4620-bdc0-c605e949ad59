import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserPhoneForm from '@/components/forms/user_phone/UserPhoneForm';

const UserPhoneCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Telefon Bilgisi Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Telefon Bilgileri', link: ROUTES.PROFILE.PHONE.INDEX },
          { title: 'Telefon Bilgisi Ekle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserPhoneForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserPhoneCreatePage;

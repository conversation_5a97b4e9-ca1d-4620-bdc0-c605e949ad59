import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserAddressListTable from '@/app/(admin)/profile/address/index/UserAddressListTable';
import { getUserAddresses } from '@/app/services/user_address.service';

const UserAddressIndexPage = async () => {
  const userAddressesResponse = await getUserAddresses();
  const userAddresses = userAddressesResponse.status ? userAddressesResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.ADDRESS.CREATE}>
            <Button type="primary">Adres Bilgisi Ekle</Button>
          </Link>
        }
        links={[{ title: 'Profil', link: ROUTES.PROFILE.INDEX }, { title: 'Adres Bilgileri' }]}
      />
      <div className="m-4">
        <UserAddressListTable initialData={userAddresses} />
      </div>
    </>
  );
};

export default UserAddressIndexPage;

'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserAddress } from '@/app/services/user_address.service';
import { UserAddressResponse } from '@/types/user_address.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaBuilding, FaHome, FaMapMarkerAlt } from 'react-icons/fa';

interface UserAddressListTableProps {
  initialData: UserAddressResponse[];
}

const UserAddressListTable = ({ initialData }: UserAddressListTableProps) => {
  const [userAddresses, setUserAddresses] = useState<UserAddressResponse[]>(initialData);
  const [filteredUserAddresses, setFilteredUserAddresses] =
    useState<UserAddressResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserAddresses = (searchValue: string) => {
    let filtered = userAddresses;

    if (searchValue) {
      filtered = filtered.filter(
        (address) =>
          address.address_line_1.toLowerCase().includes(searchValue.toLowerCase()) ||
          (address.neighborhood_street || '').toLowerCase().includes(searchValue.toLowerCase()) ||
          (address.postal_code || '').toLowerCase().includes(searchValue.toLowerCase()) ||
          (address.city || '').toLowerCase().includes(searchValue.toLowerCase()) ||
          address.country_code.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserAddresses(filtered);
  };

  useEffect(() => {
    setUserAddresses(initialData);
    setFilteredUserAddresses(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserAddresses(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserAddress(id);

    if (res.status) {
      toast.success(res.message);
      const updatedAddresses = userAddresses.filter((address) => address.id !== id);
      setUserAddresses(updatedAddresses);
      setFilteredUserAddresses(updatedAddresses);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.ADDRESS.UPDATE(id));
  };

  const getAddressTypeIcon = (type: string) => {
    switch (type) {
      case 'home':
        return <FaHome className="text-green-600" />;
      case 'work':
        return <FaBuilding className="text-blue-600" />;
      default:
        return <FaMapMarkerAlt className="text-gray-600" />;
    }
  };

  const getAddressTypeLabel = (type: string) => {
    switch (type) {
      case 'home':
        return 'Ev';
      case 'work':
        return 'İş';
      case 'other':
        return 'Diğer';
      default:
        return 'Diğer';
    }
  };

  const columns: ColumnsType<UserAddressResponse> = [
    {
      title: 'Adres Bilgisi',
      dataIndex: 'address_line_1',
      key: 'address_line_1',
      className: 'px-6 py-4',
      render: (address1: string, record: UserAddressResponse) => (
        <div className="flex items-center gap-2">
          {getAddressTypeIcon(record.type)}
          <div>
            <div className="font-medium">{address1}</div>
            {record.neighborhood_street && (
              <div className="text-xs text-gray-500">{record.neighborhood_street}</div>
            )}
            {record.address_line_2 && (
              <div className="text-xs text-gray-400">{record.address_line_2}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Tür',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      className: 'px-6 py-4',
      render: (type: string) => (
        <Tag color={type === 'home' ? 'green' : type === 'work' ? 'blue' : 'default'}>
          {getAddressTypeLabel(type)}
        </Tag>
      ),
    },
    {
      title: 'Konum',
      key: 'location',
      width: 200,
      className: 'px-6 py-4',
      render: (_: string, record: UserAddressResponse) => (
        <div>
          <div className="font-medium">{record.city || 'Bilinmiyor'}</div>
          <div className="text-xs text-gray-500">{record.country_code}</div>
          {record.postal_code && <div className="text-xs text-gray-400">{record.postal_code}</div>}
        </div>
      ),
    },
    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'gold' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserAddressResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Adres Bilgisini Sil"
            description={`"${record.address_line_1}" adres bilgisini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Adres, mahalle veya posta kodu ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserAddresses}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserAddressListTable;

import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserAddressForm from '@/components/forms/user_address/UserAddressForm';
import { getUserAddressById } from '@/app/services/user_address.service';
import { notFound } from 'next/navigation';

interface UserAddressUpdatePageProps {
  params: Promise<{
    id: number;
  }>;
}

const UserAddressUpdatePage = async ({ params }: UserAddressUpdatePageProps) => {
  const { id } = await params;

  if (isNaN(id)) {
    notFound();
  }

  const userAddressResponse = await getUserAddressById(id);

  if (!userAddressResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Adres Bilgisi Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: '<PERSON><PERSON> Bilgileri', link: ROUTES.PROFILE.ADDRESS.INDEX },
          { title: 'Adres Bilgisi Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserAddressForm mode="update" userAddress={userAddressResponse.data} />
      </KBCard>
    </>
  );
};

export default UserAddressUpdatePage;

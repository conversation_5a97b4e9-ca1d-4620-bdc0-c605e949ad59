import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserAddressForm from '@/components/forms/user_address/UserAddressForm';

const UserAddressCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Adres Bilgisi Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Adres Bilgileri', link: ROUTES.PROFILE.ADDRESS.INDEX },
          { title: '<PERSON><PERSON> Bilgisi E<PERSON>' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserAddressForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserAddressCreatePage;

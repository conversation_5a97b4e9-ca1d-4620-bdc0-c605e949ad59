import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserIdentityListTable from '@/app/(admin)/profile/identity/index/UserIdentityListTable';
import { getUserIdentities } from '@/app/services/user_identity.service';

const UserIdentityIndexPage = async () => {
  const userIdentitiesResponse = await getUserIdentities();
  const userIdentities = userIdentitiesResponse.status ? userIdentitiesResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.IDENTITY.CREATE}>
            <Button type="primary">Kimlik Bilgisi Ekle</Button>
          </Link>
        }
        links={[{ title: 'Profil', link: ROUTES.PROFILE.INDEX }, { title: 'Kimlik Bilgileri' }]}
      />
      <div className="m-4">
        <UserIdentityListTable initialData={userIdentities} />
      </div>
    </>
  );
};

export default UserIdentityIndexPage;

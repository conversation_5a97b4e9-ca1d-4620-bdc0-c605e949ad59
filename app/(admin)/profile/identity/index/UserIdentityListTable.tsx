'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserIdentity } from '@/app/services/user_identity.service';
import { UserIdentityResponse } from '@/types/user_identity.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaIdCard } from 'react-icons/fa';
import { utlMaskNumber } from '@/lib/utils';

interface UserIdentityListTableProps {
  initialData: UserIdentityResponse[];
}

const UserIdentityListTable = ({ initialData }: UserIdentityListTableProps) => {
  const [userIdentities, setUserIdentities] = useState<UserIdentityResponse[]>(initialData);
  const [filteredUserIdentities, setFilteredUserIdentities] =
    useState<UserIdentityResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);

  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserIdentities = (searchValue: string) => {
    let filtered = userIdentities;

    if (searchValue) {
      filtered = filtered.filter(
        (identity) =>
          identity.identity_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          identity.nationality_code.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserIdentities(filtered);
  };

  // Initial data değiştiğinde filtrelenmiş veriyi güncelle
  useEffect(() => {
    setUserIdentities(initialData);
    setFilteredUserIdentities(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserIdentities(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserIdentity(id);

    if (res.status) {
      toast.success(res.message);
      // State'i güncelle, API'ye tekrar istek atmak yerine
      const updatedIdentities = userIdentities.filter((identity) => identity.id !== id);
      setUserIdentities(updatedIdentities);
      setFilteredUserIdentities(updatedIdentities);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.IDENTITY.UPDATE(id));
  };

  const columns: ColumnsType<UserIdentityResponse> = [
    {
      title: 'Kimlik Bilgisi',
      dataIndex: 'identity_number',
      key: 'identity_number',
      className: 'px-6 py-4',
      render: (identityNumber: string) => (
        <div className="flex items-center gap-2">
          <FaIdCard className="text-blue-600" />
          <div>
            <div className="font-medium">{utlMaskNumber(identityNumber)}</div>
            <div className="text-xs text-gray-500">Kimlik Numarası</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Uyruk',
      dataIndex: 'nationality_code',
      key: 'nationality_code',
      width: 150,
      className: 'px-6 py-4',
      render: (nationalityCode: string) => <span className="font-medium">{nationalityCode}</span>,
    },

    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'green' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserIdentityResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Kimlik Bilgisini Sil"
            description={`"${utlMaskNumber(record.identity_number)}" kimlik bilgisini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Kimlik numarası veya uyruk kodu ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserIdentities}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserIdentityListTable;

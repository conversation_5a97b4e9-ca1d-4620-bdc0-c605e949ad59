import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserIdentityForm from '@/components/forms/user_identity/UserIdentityForm';
import { getUserIdentityById } from '@/app/services/user_identity.service';
import { notFound } from 'next/navigation';

interface UserIdentityUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const UserIdentityUpdatePage = async ({ params }: UserIdentityUpdatePageProps) => {
  const { id } = await params;
  const userIdentityId = parseInt(id);

  if (isNaN(userIdentityId)) {
    notFound();
  }

  const userIdentityResponse = await getUserIdentityById(userIdentityId);

  if (!userIdentityResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Kimlik Bilgisi Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Kimlik Bilgileri', link: ROUTES.PROFILE.IDENTITY.INDEX },
          { title: 'Kimlik Bilgisi Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserIdentityForm mode="update" userIdentity={userIdentityResponse.data} />
      </KBCard>
    </>
  );
};

export default UserIdentityUpdatePage;

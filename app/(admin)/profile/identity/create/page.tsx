import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserIdentityForm from '@/components/forms/user_identity/UserIdentityForm';

const UserIdentityCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Kimlik Bilgisi Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Kimlik Bilgileri', link: ROUTES.PROFILE.IDENTITY.INDEX },
          { title: '<PERSON><PERSON> Bilgisi <PERSON>' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserIdentityForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserIdentityCreatePage;

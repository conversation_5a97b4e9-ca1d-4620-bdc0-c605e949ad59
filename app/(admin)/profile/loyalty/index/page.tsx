import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import UserLoyaltyListTable from '@/app/(admin)/profile/loyalty/index/UserLoyaltyListTable';
import { getUserLoyalties } from '@/app/services/user_loyalty.service';

const UserLoyaltyIndexPage = async () => {
  const userLoyaltiesResponse = await getUserLoyalties();
  const userLoyalties = userLoyaltiesResponse.status ? userLoyaltiesResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.PROFILE.LOYALTY.CREATE}>
            <Button type="primary">Sadakat Programı Ekle</Button>
          </Link>
        }
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Sadakat Programı Bilgileri' },
        ]}
      />
      <div className="m-4">
        <UserLoyaltyListTable initialData={userLoyalties} />
      </div>
    </>
  );
};

export default UserLoyaltyIndexPage;

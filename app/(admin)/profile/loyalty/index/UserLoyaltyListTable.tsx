'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteUserLoyalty } from '@/app/services/user_loyalty.service';
import { UserLoyaltyResponse } from '@/types/user_loyalty.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaCreditCard } from 'react-icons/fa';

interface UserLoyaltyListTableProps {
  initialData: UserLoyaltyResponse[];
}

const UserLoyaltyListTable = ({ initialData }: UserLoyaltyListTableProps) => {
  const [userLoyalties, setUserLoyalties] = useState<UserLoyaltyResponse[]>(initialData);
  const [filteredUserLoyalties, setFilteredUserLoyalties] =
    useState<UserLoyaltyResponse[]>(initialData);
  const [deletingId, setDeletingId] = useState<number | null>(null);

  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const filterAndSearchUserLoyalties = (searchValue: string) => {
    let filtered = userLoyalties;

    if (searchValue) {
      filtered = filtered.filter(
        (loyalty) =>
          loyalty.card_number.toLowerCase().includes(searchValue.toLowerCase()) ||
          loyalty.airline?.airline_name.toLowerCase().includes(searchValue.toLowerCase()) ||
          loyalty.airline?.airline_code.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredUserLoyalties(filtered);
  };

  useEffect(() => {
    setUserLoyalties(initialData);
    setFilteredUserLoyalties(initialData);
  }, [initialData]);

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchUserLoyalties(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteUserLoyalty(id);

    if (res.status) {
      toast.success(res.message);
      const updatedLoyalties = userLoyalties.filter((loyalty) => loyalty.id !== id);
      setUserLoyalties(updatedLoyalties);
      setFilteredUserLoyalties(updatedLoyalties);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.PROFILE.LOYALTY.UPDATE(id));
  };

  const maskCardNumber = (cardNumber: string) => {
    if (cardNumber.length <= 8) return cardNumber;
    const start = cardNumber.slice(0, 4);
    const end = cardNumber.slice(-4);
    const middle = '*'.repeat(cardNumber.length - 8);
    return `${start}${middle}${end}`;
  };

  const columns: ColumnsType<UserLoyaltyResponse> = [
    {
      title: 'Havayolu & Kart Bilgisi',
      dataIndex: 'card_number',
      key: 'card_number',
      className: 'px-6 py-4',
      render: (cardNumber: string, record: UserLoyaltyResponse) => (
        <div className="flex items-center gap-2">
          <FaCreditCard className="text-blue-600" />
          <div>
            <div className="font-medium">
              {record.airline?.airline_name || 'Bilinmeyen Havayolu'}
            </div>
            <div className="text-xs text-gray-500">{maskCardNumber(cardNumber)}</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Havayolu Kodu',
      dataIndex: ['airline', 'airline_code'],
      key: 'airline_code',
      width: 120,
      className: 'px-6 py-4',
      render: (airlineCode: string) => (
        <span className="font-medium text-gray-700">{airlineCode || '-'}</span>
      ),
    },
    {
      title: 'Geçerlilik Tarihi',
      dataIndex: 'end_date',
      key: 'end_date',
      width: 150,
      className: 'px-6 py-4',
      render: (endDate: string) => (
        <span className="text-sm text-gray-600">
          {new Date(endDate).toLocaleDateString('tr-TR')}
        </span>
      ),
    },
    {
      title: 'Durum',
      dataIndex: 'is_default',
      key: 'is_default',
      width: 120,
      className: 'px-6 py-4',
      render: (isDefault: boolean) => (
        <Tag color={isDefault ? 'green' : 'default'}>{isDefault ? 'Varsayılan' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: UserLoyaltyResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Sadakat Programını Sil"
            description={`"${record.airline?.airline_name || 'Bilinmeyen'}" havayolu sadakat programını silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-600 transition-all duration-200 hover:bg-red-50 hover:text-red-700"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Kart numarası, havayolu adı veya kodu ile arayın..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredUserLoyalties}
          rowKey="id"
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          className="modern-table"
          scroll={{ x: 800 }}
        />
      </KBCard>
    </div>
  );
};

export default UserLoyaltyListTable;

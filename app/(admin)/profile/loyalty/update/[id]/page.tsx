import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserLoyaltyForm from '@/components/forms/user_loyalty/UserLoyaltyForm';
import { getUserLoyaltyById } from '@/app/services/user_loyalty.service';
import { notFound } from 'next/navigation';

interface UserLoyaltyUpdatePageProps {
  params: Promise<{
    id: number;
  }>;
}

const UserLoyaltyUpdatePage = async ({ params }: UserLoyaltyUpdatePageProps) => {
  const { id } = await params;

  if (isNaN(id)) {
    notFound();
  }

  const userLoyaltyResponse = await getUserLoyaltyById(id);

  if (!userLoyaltyResponse.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Sadakat Programı Güncelle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Sadakat Programı Bilgileri', link: ROUTES.PROFILE.LOYALTY.INDEX },
          { title: 'Sadakat Programı Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserLoyaltyForm mode="update" userLoyalty={userLoyaltyResponse.data} />
      </KBCard>
    </>
  );
};

export default UserLoyaltyUpdatePage;

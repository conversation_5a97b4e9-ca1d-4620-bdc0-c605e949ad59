import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UserLoyaltyForm from '@/components/forms/user_loyalty/UserLoyaltyForm';

const UserLoyaltyCreatePage = async () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Sadakat Programı Ekle"
        links={[
          { title: 'Profil', link: ROUTES.PROFILE.INDEX },
          { title: 'Sadakat Programı Bilgileri', link: ROUTES.PROFILE.LOYALTY.INDEX },
          { title: 'Sadakat Programı Ekle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UserLoyaltyForm mode="create" />
      </KBCard>
    </>
  );
};

export default UserLoyaltyCreatePage;

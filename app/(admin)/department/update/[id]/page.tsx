import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateDepartmentForm from '@/components/forms/department/UpdateDepartmentForm';
import { getDepartments, getDepartmentById } from '@/app/services/department.service';
import { notFound } from 'next/navigation';

interface DepartmentUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const DepartmentUpdatePage = async ({ params }: DepartmentUpdatePageProps) => {
  const { id } = await params;
  const departmentId = parseInt(id);
  
  if (isNaN(departmentId)) {
    notFound();
  }

  const [departmentResponse, departmentsResponse] = await Promise.all([
    getDepartmentById(departmentId),
    getDepartments()
  ]);

  if (!departmentResponse.status || !departmentResponse.data) {
    notFound();
  }

  const department = departmentResponse.data;
  const departments = departmentsResponse.status ? departmentsResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content="Departman Güncelle"
        links={[
          { title: 'Departmanlar', link: ROUTES.DEPARTMENT.INDEX },
          { title: 'Departman Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UpdateDepartmentForm 
          department={department}
          departments={departments.filter(d => d.id !== departmentId)} 
        />
      </KBCard>
    </>
  );
};

export default DepartmentUpdatePage;

import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import DepartmentListTable from '@/app/(admin)/department/index/DepartmentListTable';

const DepartmentIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.DEPARTMENT.CREATE}>
            <Button type="primary">Departman Ekle</Button>
          </Link>
        }
        links={[{ title: 'Departmanlar' }]}
      />
      <div className="m-4">
        <DepartmentListTable />
      </div>
    </>
  );
};

export default DepartmentIndexPage;

"use client";

import { DepartmentResponse } from '@/types/department.types';
import { FaRegBuilding } from 'react-icons/fa';
import { Button, Space, Popconfirm } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { deleteDepartment } from '@/app/services/department.service';
import { toast } from 'sonner';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';


export const DepartmentItem = ({
  department,
  allDepartments,
}: {
  department: DepartmentResponse;
  allDepartments: DepartmentResponse[];
}) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const childDepartments = allDepartments.filter((d) => d.parent_id === department.id);

  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    const res = await deleteDepartment(id);

    if (res.status) {
      toast.success(res.message);
      router.push(ROUTES.DEPARTMENT.INDEX);
    } else {
      toast.error(res.message);
    }

    setIsDeleting(false);
  };

  const handleUpdate = () => {
    router.push(ROUTES.DEPARTMENT.UPDATE(department.id));
  };

  return (
    <li className="ml-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <FaRegBuilding className="text-sm text-blue-950" />
            <span>{department.name}</span>
            {childDepartments.length > 0 && (
              <span className="text-sm text-gray-500">
                ({childDepartments.length} alt departman)
              </span>
            )}
          </div>
          <span className="text-xs text-blue-400">{department.description}</span>
        </div>
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={handleUpdate}
            className="text-xs"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Departmanı Sil"
            description={`"${department.name}" departmanını silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(department.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={isDeleting}
              disabled={isDeleting}
              className="text-xs"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      </div>
      {childDepartments.length > 0 && (
        <ul className="mt-2 space-y-2">
          {childDepartments.map((child) => (
            <DepartmentItem key={child.id} department={child} allDepartments={allDepartments} />
          ))}
        </ul>
      )}
    </li>
  );
};

import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import {ROUTES} from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateDepartmentForm from '@/components/forms/department/CreateDepartmentForm';
import { getDepartments } from '@/app/services/department.service';

const DepartmentCreatePage = async () => {
  const departments = await getDepartments();
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Departman Ekle"
        links={[
          { title: 'Departmanlar', link: ROUTES.DEPARTMENT.INDEX },
          { title: 'Departman Ekle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <CreateDepartmentForm departments={departments.status ? departments.data.items : []} />
      </KBCard>
    </>
  );
};

export default DepartmentCreatePage;

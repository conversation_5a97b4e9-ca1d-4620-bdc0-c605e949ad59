import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateReasonForm from '@/components/forms/reason/UpdateReasonForm';
import { getReasonById } from '@/app/services/reason.service';
import { notFound } from 'next/navigation';

interface ReasonUpdatePageProps {
  params: Promise<{ id: string }>;
}

const ReasonUpdatePage = async ({ params }: ReasonUpdatePageProps) => {
  const { id } = await params;
  const reasonId = parseInt(id);

  if (isNaN(reasonId)) {
    notFound();
  }

  const reason = await getReasonById(reasonId);

  if (!reason.status) {
    notFound();
  }

  return (
    <>
      <HeaderBreadcrumb
        content="Açıklama Düzenle"
        links={[{ title: 'Açıklamalar', link: ROUTES.REASON.INDEX }, { title: 'Açıklama Düzenle' }]}
      />
      <KBCard className="m-4 p-4">
        <UpdateReasonForm reason={reason.data} />
      </KBCard>
    </>
  );
};

export default ReasonUpdatePage;

import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateReasonForm from '@/components/forms/reason/CreateReasonForm';

const ReasonCreatePage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Açıklama Ekle"
        links={[
          { title: 'Açıklamalar', link: ROUTES.REASON.INDEX },
          { title: 'Açıkla<PERSON> Ekle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <CreateReasonForm />
      </KBCard>
    </>
  );
};

export default ReasonCreatePage;

'use client';

import { ChangeEvent, useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Select, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import KBCard from '@/components/ui/custom/KBCard';
import { deleteReason, getReasons } from '@/app/services/reason.service';
import { ReasonResponse } from '@/types/reason.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';

const ReasonList = () => {
  const [reasons, setReasons] = useState<ReasonResponse[]>([]);
  const [filteredReasons, setFilteredReasons] = useState<ReasonResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const fetchReasons = async (filterType?: string) => {
    try {
      setLoading(true);
      const type = filterType === 'all' ? undefined : filterType;
      const response = await getReasons(type);
      if (response.status) {
        setReasons(response.data.items);
        setFilteredReasons(response.data.items);
      }
    } catch {
      toast.error('Veriler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadReasons = async () => {
      await fetchReasons();
    };

    void loadReasons();
  }, []);

  const filterAndSearchReasons = (searchValue: string, filterValue: string) => {
    let filtered = reasons;

    if (filterValue !== 'all') {
      filtered = filtered.filter((reason) => reason.type === filterValue);
    }

    if (searchValue) {
      filtered = filtered.filter((reason) =>
        reason.name.toLowerCase().includes(searchValue.toLowerCase()),
      );
    }

    setFilteredReasons(filtered);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
    filterAndSearchReasons(searchText, value);
  };

  const handleSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchReasons(value, selectedFilter);
  };

  const filterOptions = [
    { value: 'all', label: 'Tümü' },
    { value: 'rejection_reason', label: 'Red Edilme Nedeni' },
    { value: 'violation_explanation', label: 'İhlal Açıklaması' },
    { value: 'travel_reason', label: 'Seyahat Sebebi' },
  ];

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteReason(id);

    if (res.status) {
      toast.success(res.message);
      await fetchReasons(selectedFilter);
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.REASON.UPDATE(id));
  };

  const getTypeLabel = (type: string) => {
    const typeLabels = {
      travel_reason: {
        label: 'Seyahat Sebebi',
        color: 'blue',
        className: 'bg-blue-100 text-blue-800 border-blue-200',
      },
      violation_explanation: {
        label: 'İhlal Açıklaması',
        color: 'orange',
        className: 'bg-orange-100 text-orange-800 border-orange-200',
      },
      rejection_reason: {
        label: 'Red Edilme Nedeni',
        color: 'red',
        className: 'bg-red-100 text-red-800 border-red-200',
      },
    };
    return (
      typeLabels[type as keyof typeof typeLabels] || {
        label: type,
        color: 'default',
        className: 'bg-gray-100 text-gray-800 border-gray-200',
      }
    );
  };

  const columns: ColumnsType<ReasonResponse> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      className: 'px-6 py-4',
    },
    {
      title: 'Ad',
      dataIndex: 'name',
      key: 'name',
      className: 'px-6 py-4',
    },
    {
      title: 'Tip',
      dataIndex: 'type',
      key: 'type',
      width: 200,
      className: 'px-6 py-4',
      render: (type: string) => {
        const typeInfo = getTypeLabel(type);
        return (
          <span
            className={`inline-flex items-center rounded-full border px-3 py-1 text-xs font-medium ${typeInfo.className}`}
          >
            {typeInfo.label}
          </span>
        );
      },
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: ReasonResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="rounded-lg border-0 px-4 py-2 text-blue-600 transition-all duration-200 hover:bg-blue-50 hover:text-blue-700"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Açıklamayı Sil"
            description={`"${record.name}" açıklamasını silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="rounded-lg border-0 px-4 py-2 text-red-500 transition-all duration-200 hover:bg-red-50 hover:text-red-600"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* Modern Filtre ve Arama Bölümü */}
      <KBCard>
        <div className="flex flex-col gap-6 px-6 lg:flex-row lg:items-center lg:justify-between">
          {/* Sol Taraf - Filtreler */}
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <FilterOutlined className="mr-1 text-gray-500" />
                Açıklama Tipi
              </label>
              <Select
                placeholder="Tip seçiniz"
                value={selectedFilter}
                onChange={handleFilterChange}
                options={filterOptions}
                className="w-full"
                size="middle"
                allowClear
                onClear={() => {
                  setSelectedFilter('all');
                  filterAndSearchReasons(searchText, 'all');
                }}
              />
            </div>

            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Açıklama adında ara..."
                value={searchText}
                onChange={handleSearchChange}
                prefix={<SearchOutlined className="text-slate-400" />}
                size="middle"
                allowClear
                className=""
              />
            </div>
          </div>

          {/* Sağ Taraf - İstatistikler */}
          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-800">{filteredReasons.length}</div>
              <div className="text-xs text-gray-500">Gösterilen</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{reasons.length}</div>
              <div className="text-xs text-gray-500">Toplam</div>
            </div>
          </div>
        </div>
      </KBCard>

      {/* Modern Table Bölümü */}
      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredReasons}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) => `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          scroll={{ x: 'max-content' }}
          size="large"
          className="modern-table -mt-5 [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
        />
      </KBCard>
    </div>
  );
};

export default ReasonList;

import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import { Button, Empty } from 'antd';
import KBCard from '@/components/ui/custom/KBCard';
import ReasonList from '@/app/(admin)/reason/index/ReasonList';
import { getReasons } from '@/app/services/reason.service';

const ReasonPage = async () => {
  const response = await getReasons();
  const reasons = response.status ? response.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.REASON.CREATE}>
            <Button type="primary">Açıklama Ekle</Button>
          </Link>
        }
        links={[{ title: 'Açıklamalar' }]}
      />
      <div className="m-4">
        {reasons.length === 0 ? (
          <KBCard className="p-8 text-center">
            <Empty description="Henüz açıklama eklenmemiş" />
          </KBCard>
        ) : (

            <ReasonList />
        )}
      </div>
    </>
  );
};

export default ReasonPage;

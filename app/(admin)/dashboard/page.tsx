import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Card } from 'antd';
import {
  FiActivity,
  FiCalendar,
  FiClock,
  FiMapPin,
  FiTrendingUp,
  FiUserCheck,
  FiUsers,
} from 'react-icons/fi';
import { Building } from 'lucide-react';

const Dashboard = () => {
  const statsData = [
    {
      title: 'Toplam Kullanıcı',
      value: 1234,
      icon: <FiUsers className="text-2xl text-blue-600" />,
      change: '+12%',
      changeType: 'increase' as const,
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
    {
      title: 'Akt<PERSON>',
      value: 45,
      icon: <Building className="text-2xl text-green-600" />,
      change: '+5%',
      changeType: 'increase' as const,
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    {
      title: '<PERSON><PERSON> Sayı<PERSON>ı',
      value: 28,
      icon: <FiMapPin className="text-2xl text-purple-600" />,
      change: '+2%',
      changeType: 'increase' as const,
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
    },
    {
      title: 'Misafir Kayıtları',
      value: 892,
      icon: <FiUserCheck className="text-2xl text-orange-600" />,
      change: '+18%',
      changeType: 'increase' as const,
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      action: 'Yeni kullanıcı kaydı',
      user: 'Ahmet Yılmaz',
      time: '5 dakika önce',
      type: 'user',
    },
    {
      id: 2,
      action: 'Departman güncellendi',
      user: 'Ayşe Kaya',
      time: '15 dakika önce',
      type: 'department',
    },
    {
      id: 3,
      action: 'Misafir kaydı oluşturuldu',
      user: 'Mehmet Demir',
      time: '1 saat önce',
      type: 'guest',
    },
    {
      id: 4,
      action: 'Şube bilgileri güncellendi',
      user: 'Fatma Özkan',
      time: '2 saat önce',
      type: 'branch',
    },
  ];

  return (
    <>
      <HeaderBreadcrumb content="Dashboard" links={[{ title: 'Dashboard' }]} />

      <div className="space-y-6 p-6">
        {/* İstatistik Kartları */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
          {statsData.map((stat, index) => (
            <Card
              key={index}
              className={`${stat.bgColor} ${stat.borderColor} transform border-l-4 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="mb-2 flex items-center gap-3">
                    {stat.icon}
                    <h3 className="text-sm font-medium text-gray-600">{stat.title}</h3>
                  </div>
                  <div className="flex items-end gap-2">
                    <span className="text-2xl font-bold text-gray-900">
                      {stat.value.toLocaleString()}
                    </span>
                    <span
                      className={`rounded-full px-2 py-1 text-xs font-medium ${
                        stat.changeType === 'increase'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Ana İçerik Alanı */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Son Aktiviteler */}
          <div className="lg:col-span-2">
            <Card
              title={
                <div className="flex items-center gap-2">
                  <FiActivity className="text-lg text-blue-600" />
                  <span className="text-lg font-semibold text-gray-800">Son Aktiviteler</span>
                </div>
              }
              className="h-full shadow-sm transition-shadow duration-300 hover:shadow-md"
            >
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center gap-4 rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100"
                  >
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full ${
                        activity.type === 'user'
                          ? 'bg-blue-100 text-blue-600'
                          : activity.type === 'department'
                            ? 'bg-green-100 text-green-600'
                            : activity.type === 'guest'
                              ? 'bg-orange-100 text-orange-600'
                              : 'bg-purple-100 text-purple-600'
                      }`}
                    >
                      {activity.type === 'user' ? (
                        <FiUsers className="text-sm" />
                      ) : activity.type === 'department' ? (
                        <Building className="text-sm" />
                      ) : activity.type === 'guest' ? (
                        <FiUserCheck className="text-sm" />
                      ) : (
                        <FiMapPin className="text-sm" />
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                      <p className="text-xs text-gray-500">{activity.user}</p>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-400">
                      <FiClock className="text-xs" />
                      {activity.time}
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>

          {/* Hızlı İstatistikler */}
          <div className="space-y-6">
            <Card
              title={
                <div className="flex items-center gap-2">
                  <FiTrendingUp className="text-lg text-green-600" />
                  <span className="text-lg font-semibold text-gray-800">Bu Ay</span>
                </div>
              }
              className="shadow-sm transition-shadow duration-300 hover:shadow-md"
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-lg bg-blue-50 p-3">
                  <span className="text-sm font-medium text-gray-700">Yeni Kayıtlar</span>
                  <span className="text-lg font-bold text-blue-600">156</span>
                </div>
                <div className="flex items-center justify-between rounded-lg bg-green-50 p-3">
                  <span className="text-sm font-medium text-gray-700">Güncellemeler</span>
                  <span className="text-lg font-bold text-green-600">89</span>
                </div>
                <div className="flex items-center justify-between rounded-lg bg-orange-50 p-3">
                  <span className="text-sm font-medium text-gray-700">Aktif Oturumlar</span>
                  <span className="text-lg font-bold text-orange-600">234</span>
                </div>
              </div>
            </Card>

            <Card
              title={
                <div className="flex items-center gap-2">
                  <FiCalendar className="text-lg text-purple-600" />
                  <span className="text-lg font-semibold text-gray-800">Bugün</span>
                </div>
              }
              className="!mt-4 shadow-sm transition-shadow duration-300 hover:shadow-md"
            >
              <div className="space-y-3">
                <div className="rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 p-4 text-center">
                  <div className="mb-1 text-2xl font-bold text-gray-900">
                    {new Date().toLocaleDateString('tr-TR', {
                      day: 'numeric',
                      month: 'long',
                      year: 'numeric',
                    })}
                  </div>
                  <div className="text-sm text-gray-600">
                    {new Date().toLocaleDateString('tr-TR', { weekday: 'long' })}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3">
                  <div className="rounded-lg bg-gray-50 p-3 text-center">
                    <div className="text-lg font-bold text-gray-900">42</div>
                    <div className="text-xs text-gray-600">Giriş</div>
                  </div>
                  <div className="rounded-lg bg-gray-50 p-3 text-center">
                    <div className="text-lg font-bold text-gray-900">18</div>
                    <div className="text-xs text-gray-600">İşlem</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </>
  );
};

export default Dashboard;

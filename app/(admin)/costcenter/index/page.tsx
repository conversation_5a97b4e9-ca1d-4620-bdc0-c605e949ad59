import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import CostCenterListTable from '@/app/(admin)/costcenter/index/CostCenterListTable';

const CostCenterIndexPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.COSTCENTER.CREATE}>
            <Button type="primary">Masraf <PERSON>rk<PERSON><PERSON></Button>
          </Link>
        }
        links={[{ title: 'Masra<PERSON>ler<PERSON>' }]}
      />
      <div className="m-4">
        <CostCenterListTable />
      </div>
    </>
  );
};

export default CostCenterIndexPage;

'use client';

import { useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Space, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteCostCenter, getCostCenters } from '@/app/services/constcenter.service';
import { CostCenterResponse } from '@/types/costcenter.types';
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { BiCoinStack } from 'react-icons/bi';

const CostCenterListTable = () => {
  const [costCenters, setCostCenters] = useState<CostCenterResponse[]>([]);
  const [filteredCostCenters, setFilteredCostCenters] = useState<CostCenterResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const fetchCostCenters = async () => {
    try {
      setLoading(true);
      const response = await getCostCenters();
      if (response.status) {
        setCostCenters(response.data.items);
        setFilteredCostCenters(response.data.items);
      }
    } catch {
      toast.error('Veriler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCostCenters();
  }, []);

  const filterCostCenters = (searchValue: string) => {
    if (!searchValue.trim()) {
      setFilteredCostCenters(costCenters);
      return;
    }

    const filtered = costCenters.filter((costCenter) => {
      const nameMatch = costCenter.name.toLowerCase().includes(searchValue.toLowerCase());
      const codeMatch = costCenter.code.toLowerCase().includes(searchValue.toLowerCase());
      
      const departmentMatch = costCenter.departments?.some(dept => 
        dept.name.toLowerCase().includes(searchValue.toLowerCase())
      );
      
      const jobtitleMatch = costCenter.jobtitles?.some(job => 
        job.name.toLowerCase().includes(searchValue.toLowerCase())
      );

      return nameMatch || codeMatch || departmentMatch || jobtitleMatch;
    });

    setFilteredCostCenters(filtered);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterCostCenters(value);
  };

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteCostCenter(id);

    if (res.status) {
      toast.success(res.message);
      await fetchCostCenters();
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.COSTCENTER.UPDATE(id));
  };

  const getDepartmentNames = (costCenter: CostCenterResponse) => {
    if (!costCenter.departments || !Array.isArray(costCenter.departments)) return [];
    return costCenter.departments.map(dept => dept.name);
  };

  const getJobtitleNames = (costCenter: CostCenterResponse) => {
    if (!costCenter.jobtitles || !Array.isArray(costCenter.jobtitles)) return [];
    return costCenter.jobtitles.map(job => job.name);
  };

  const columns: ColumnsType<CostCenterResponse> = [
    {
      title: 'Masraf Merkezi',
      dataIndex: 'name',
      key: 'name',
      className: 'px-6 py-4',
      render: (name: string, record: CostCenterResponse) => (
        <div className="flex items-center gap-2">
          <BiCoinStack className="text-blue-600" />
          <div>
            <div className="font-medium">{name}</div>
            <div className="text-xs text-gray-500">({record.code})</div>
          </div>
        </div>
      ),
    },
    {
      title: 'Departmanlar',
      dataIndex: 'departments',
      key: 'departments',
      className: 'px-6 py-4',
      render: (_: unknown, record: CostCenterResponse) => {
        const departmentNames = getDepartmentNames(record);
        return (
          <div className="flex flex-wrap gap-1">
            {departmentNames.length > 0 ? (
              departmentNames.map((name, index) => (
                <Tag key={index} color="blue" className="text-xs">
                  {name}
                </Tag>
              ))
            ) : (
              <span className="text-xs text-gray-400 italic">Atanmamış</span>
            )}
          </div>
        );
      },
    },
    {
      title: 'Pozisyonlar',
      dataIndex: 'jobtitles',
      key: 'jobtitles',
      className: 'px-6 py-4',
      render: (_: unknown, record: CostCenterResponse) => {
        const jobtitleNames = getJobtitleNames(record);
        return (
          <div className="flex flex-wrap gap-1">
            {jobtitleNames.length > 0 ? (
              jobtitleNames.map((name, index) => (
                <Tag key={index} color="green" className="text-xs">
                  {name}
                </Tag>
              ))
            ) : (
              <span className="text-xs text-gray-400 italic">Atanmamış</span>
            )}
          </div>
        );
      },
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: CostCenterResponse) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 border-0 px-4 py-2"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Masraf Merkezini Sil"
            description={`"${record.name}" masraf merkezini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200 border-0 px-4 py-2"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between px-6">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Masraf merkezi, kod, departman veya pozisyon ara..."
                value={searchText}
                onChange={handleSearchChange}
                className="w-full"
                size="middle"
                allowClear
                onClear={() => {
                  setSearchText('');
                  filterCostCenters('');
                }}
              />
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredCostCenters}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          scroll={{ x: 'max-content' }}
          size="large"
          className="-mt-6 modern-table [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
        />
      </KBCard>
    </div>
  );
};

export default CostCenterListTable;

'use client';

import { But<PERSON>, Popconfirm, Space, Tag } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { BiCoinStack } from 'react-icons/bi';
import { ROUTES } from '@/lib/routes';
import { CostCenterResponse } from '@/types/costcenter.types';
import { deleteCostCenter } from '@/app/services/constcenter.service';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { useState } from 'react';

interface CostCenterItemProps {
  costCenter: CostCenterResponse;
}

export const CostCenterItem = ({ costCenter }: CostCenterItemProps) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    const res = await deleteCostCenter(id);

    if (res.status) {
      toast.success(res.message);
      router.refresh();
    } else {
      toast.error(res.message);
    }

    setIsDeleting(false);
  };

  const handleUpdate = () => {
    router.push(ROUTES.COSTCENTER.UPDATE(costCenter.id));
  };

  const getDepartmentNames = () => {
    if (!costCenter.departments || !Array.isArray(costCenter.departments)) return [];
    return costCenter.departments.map((dept) => dept.name);
  };

  const getJobtitleNames = () => {
    if (!costCenter.jobtitles || !Array.isArray(costCenter.jobtitles)) return [];
    return costCenter.jobtitles.map((job) => job.name);
  };

  return (
    <li className="ml-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <BiCoinStack className="text-sm text-blue-950" />
            <span>{costCenter.name}</span>
            <span className="text-sm text-gray-500">({costCenter.code})</span>
          </div>

          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600">Departmanlar:</span>
              <div className="inline-flex flex-wrap gap-1">
                {getDepartmentNames().length > 0 ? (
                  getDepartmentNames().map((name, index) => (
                    <Tag key={index} color="blue" className="text-xs">
                      {name}
                    </Tag>
                  ))
                ) : (
                  <span className="text-xs text-gray-400 italic">Atanmamış</span>
                )}
              </div>
            </div>

            <div className="flex items-center gap-1">
              <span className="text-xs text-gray-600">Pozisyonlar:</span>
              <div className="inline-flex flex-wrap gap-1">
                {getJobtitleNames().length > 0 ? (
                  getJobtitleNames().map((name, index) => (
                    <Tag key={index} color="green" className="text-xs">
                      {name}
                    </Tag>
                  ))
                ) : (
                  <span className="text-xs text-gray-400 italic">Atanmamış</span>
                )}
              </div>
            </div>
          </div>
        </div>

        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={handleUpdate}
            className="text-xs"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Masraf Merkezini Sil"
            description={`"${costCenter.name}" masraf merkezini silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(costCenter.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={isDeleting}
              disabled={isDeleting}
              className="text-xs"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      </div>
    </li>
  );
};

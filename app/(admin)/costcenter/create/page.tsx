import { ROUTES } from '@/lib/routes';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import KBCard from '@/components/ui/custom/KBCard';
import CreateCostCenterForm from '@/components/forms/cost-center/CreateCostCenterForm';
import { getDepartments } from '@/app/services/department.service';
import { getJobtitles } from '@/app/services/jobtitle.service';

const CostCenterCreatePage = async () => {
  const [departments, jobtitles] = await Promise.all([
    getDepartments(),
    getJobtitles(),
  ]);

  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Ma<PERSON>raf <PERSON>ez<PERSON>"
        links={[
          { title: '<PERSON><PERSON><PERSON><PERSON>', link: ROUTES.COSTCENTER.INDEX },
          { title: '<PERSON><PERSON><PERSON><PERSON>' },
        ]}
      />

      <KBCard className="m-4 p-4">
        <CreateCostCenterForm
          departments={departments.status ? departments.data.items : []}
          jobtitles={jobtitles.status ? jobtitles.data.items : []}
        />
      </KBCard>
    </>
  );
};

export default CostCenterCreatePage;

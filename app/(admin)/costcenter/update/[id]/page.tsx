import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateCostCenterForm from '@/components/forms/cost-center/UpdateCostCenterForm';
import { getCostCenterById } from '@/app/services/constcenter.service';
import { getDepartments } from '@/app/services/department.service';
import { getJobtitles } from '@/app/services/jobtitle.service';
import { notFound } from 'next/navigation';

interface CostCenterUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const CostCenterUpdatePage = async ({ params }: CostCenterUpdatePageProps) => {
  const { id } = await params;
  const costCenterId = parseInt(id);

  if (isNaN(costCenterId)) {
    notFound();
  }

  const [costCenterResponse, departments, jobtitles] = await Promise.all([
    getCostCenterById(costCenterId),
    getDepartments(),
    getJobtitles(),
  ]);

  if (!costCenterResponse.status || !costCenterResponse.data) {
    notFound();
  }

  const costCenter = costCenterResponse.data;

  return (
    <>
      <HeaderBreadcrumb
        content="Masraf Merkezi Güncelle"
        links={[
          { title: 'Masraf Merkezleri', link: ROUTES.COSTCENTER.INDEX },
          { title: 'Masraf Merkezi Güncelle' },
        ]}
      />

      <KBCard className="m-4 p-4">
        <UpdateCostCenterForm
          costCenter={costCenter}
          departments={departments.status ? departments.data.items : []}
          jobtitles={jobtitles.status ? jobtitles.data.items : []}
        />
      </KBCard>
    </>
  );
};

export default CostCenterUpdatePage;

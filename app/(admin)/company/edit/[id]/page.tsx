'use client';

import KBCard from '@/components/ui/custom/KBCard';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from '@/components/ui/button';
import { useParams } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { Form, Input } from 'antd';

const CompanyEditPage = () => {
  const params = useParams();
  const companyId = params.id;

  return (
    <>
      <HeaderBreadcrumb
        content="Firma Düzenle"
        links={[{ title: 'Firmalar', link: ROUTES.COMPANY.INDEX }, { title: 'Firma Düzenle' }]}
      />
      <KBCard className="m-4 p-4">
        <Form layout="vertical">
          <Form.Item label="Kullanıcı Adı">
            <Input />
          </Form.Item>

          <Form.Item label="E-posta">
            <Input />
          </Form.Item>

          <Form.Item>
            <Button>Kaydet {companyId}</Button>
          </Form.Item>
        </Form>
      </KBCard>
    </>
  );
};

export default CompanyEditPage;

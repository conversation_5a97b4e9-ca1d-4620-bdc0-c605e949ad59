'use client';

import KBCard from '@/components/ui/custom/KBCard';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import {ROUTES} from '@/lib/routes';
import CreateCompanyForm from '@/components/forms/company/CreateCompanyForm';

const CompanyCreatePage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Firma Ekle"
        links={[{ title: 'Firmalar', link: ROUTES.COMPANY.INDEX }, { title: 'Firma Ekle' }]}
      />
      <KBCard className="m-4 p-4">
        <CreateCompanyForm />
      </KBCard>
    </>
  );
};

export default CompanyCreatePage;

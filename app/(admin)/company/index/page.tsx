import KBCard from '@/components/ui/custom/KBCard';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import Link from 'next/link';
import {AiOutlineEdit} from 'react-icons/ai';
import {BiDetail} from 'react-icons/bi';
import {ROUTES} from '@/lib/routes';
import {CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Button} from 'antd';
import {Table, TableBody, TableCell, TableHead, TableHeader, TableRow,} from '@/components/ui/table';
import KBAvatar from '@/components/ui/custom/KbAvatar';

const CompanyPage = () => {
  return (
    <div className="space-y-4">
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.COMPANY.CREATE}>
            <Button type="primary">Firma Ekle</Button>
          </Link>
        }
        links={[{ title: 'Firmalar' }]}
      />
      <KBCard className="m-4">
        <CardHeader>
          <CardTitle>
            <h1 className="text-kb-primary">Tüm Fi<PERSON>ar</h1>
          </CardTitle>
          <CardDescription>
            <p>Şirketlerinizi buradan yönetebilirsiniz.</p>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[100px]">ID</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Ad</TableHead>
                <TableHead className="text-right">Oluşturulma Tarihi</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 10 }).map((_, index) => (
                <TableRow key={`company-row-${index}`}>
                  <TableCell className="font-medium">
                    <KBAvatar src="https://github.com/shadcn.png" fallback="NS" size={40} />
                  </TableCell>
                  <TableCell>Aktif</TableCell>
                  <TableCell>Firma {index + 1}</TableCell>
                  <TableCell className="text-right">2024-03-{index + 1}</TableCell>
                  <TableCell className="space-x-1 text-right">
                    <Link href={ROUTES.COMPANY.EDIT(index + 1)}>
                      <Button className="rounded-xs">
                        <AiOutlineEdit />
                      </Button>
                    </Link>
                    <Link href={ROUTES.COMPANY.SHOW(index + 1)}>
                      <Button className="rounded-xs">
                        <BiDetail />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </KBCard>
    </div>
  );
};

export default CompanyPage;

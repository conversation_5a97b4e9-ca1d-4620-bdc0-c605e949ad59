import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateJobtitleForm from '@/components/forms/jobtitle/UpdateJobtitleForm';
import { getJobtitles, getJobtitleById } from '@/app/services/jobtitle.service';
import { notFound } from 'next/navigation';

interface JobtitleUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const JobtitleUpdatePage = async ({ params }: JobtitleUpdatePageProps) => {
  const { id } = await params;
  const jobtitleId = parseInt(id);
  
  if (isNaN(jobtitleId)) {
    notFound();
  }

  const [jobtitleResponse, jobtitlesResponse] = await Promise.all([
    getJobtitleById(jobtitleId),
    getJobtitles()
  ]);

  if (!jobtitleResponse.status || !jobtitleResponse.data) {
    notFound();
  }

  const jobtitle = jobtitleResponse.data;
  const jobtitles = jobtitlesResponse.status ? jobtitlesResponse.data.items : [];

  return (
    <>
      <HeaderBreadcrumb
        content="Pozisyon Güncelle"
        links={[
          { title: 'Pozisyonlar', link: ROUTES.JOBTITLE.INDEX },
          { title: 'Pozisyon Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UpdateJobtitleForm 
          jobtitle={jobtitle}
          jobtitles={jobtitles.filter(j => j.id !== jobtitleId)} 
        />
      </KBCard>
    </>
  );
};

export default JobtitleUpdatePage;

import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import CreateJobtitleForm from '@/components/forms/jobtitle/CreateJobtitleForm';
import { getJobtitles } from '@/app/services/jobtitle.service';

const JobtitleCreatePage = async () => {
  const jobtitles = await getJobtitles();

  return (
    <>
      <HeaderBreadcrumb
        content="Yeni Pozisyon Ekle"
        links={[{ title: 'Pozisyonlar', link: ROUTES.JOBTITLE.INDEX }, { title: 'Pozisy<PERSON> Ekle' }]}
      />

      <KBCard className="m-4 p-4">
        <CreateJobtitleForm jobtitles={jobtitles.status ? jobtitles.data.items : []} />
      </KBCard>
    </>
  );
};

export default JobtitleCreatePage;

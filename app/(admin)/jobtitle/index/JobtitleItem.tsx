"use client";

import { JobtitleResponse } from '@/types/jobtitle.types';
import { FaUserTie } from 'react-icons/fa';
import { Button, Space, Popconfirm } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { deleteJobtitle } from '@/app/services/jobtitle.service';
import { toast } from 'sonner';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export const JobtitleItem = ({
  jobtitle,
  allJobtitles,
}: {
  jobtitle: JobtitleResponse;
  allJobtitles: JobtitleResponse[];
}) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);
  const childJobtitles = allJobtitles.filter((j) => j.parent_id === jobtitle.id);

  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    const res = await deleteJobtitle(id);

    if (res.status) {
      toast.success(res.message);
      router.push(ROUTES.JOBTITLE.INDEX);
    } else {
      toast.error(res.message);
    }

    setIsDeleting(false);
  };

  const handleUpdate = () => {
    router.push(ROUTES.JOBTITLE.UPDATE(jobtitle.id));
  };

  return (
    <li className="ml-4">
      <div className="flex items-center justify-between">
        <div className="flex flex-col gap-1">
          <div className="flex items-center gap-1">
            <FaUserTie className="text-sm text-blue-950" />
            <span>{jobtitle.name}</span>
            {childJobtitles.length > 0 && (
              <span className="text-sm text-gray-500">
                ({childJobtitles.length} alt pozisyon)
              </span>
            )}
          </div>
          <span className="text-xs text-blue-400">{jobtitle.description}</span>
        </div>
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={handleUpdate}
            className="text-xs"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Pozisyonu Sil"
            description={`"${jobtitle.name}" pozisyonunu silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(jobtitle.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
          >
            <Button
              type="text"
              size="small"
              danger
              icon={<DeleteOutlined />}
              loading={isDeleting}
              disabled={isDeleting}
              className="text-xs"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      </div>
      {childJobtitles.length > 0 && (
        <ul className="mt-2 space-y-2">
          {childJobtitles.map((child) => (
            <JobtitleItem key={child.id} jobtitle={child} allJobtitles={allJobtitles} />
          ))}
        </ul>
      )}
    </li>
  );
};

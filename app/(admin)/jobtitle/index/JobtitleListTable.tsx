'use client';

import { useEffect, useState } from 'react';
import { Button, Input, Popconfirm, Select, Space, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import { DeleteOutlined, EditOutlined, FilterOutlined, SearchOutlined } from '@ant-design/icons';
import { deleteJobtitle, getJobtitles } from '@/app/services/jobtitle.service';
import { JobtitleResponse } from '@/types/jobtitle.types';

type JobtitleTreeNode = JobtitleResponse & {
  children?: JobtitleTreeNode[];
};
import { useRouter } from 'next/navigation';
import { ROUTES } from '@/lib/routes';
import { toast } from 'sonner';
import KBCard from '@/components/ui/custom/KBCard';
import { FaUserTie } from 'react-icons/fa';

const JobtitleListTable = () => {
  const [jobtitles, setJobtitles] = useState<JobtitleResponse[]>([]);
  const [filteredJobtitles, setFilteredJobtitles] = useState<JobtitleResponse[]>([]);
  const [loading, setLoading] = useState(true);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [searchText, setSearchText] = useState<string>('');
  const router = useRouter();

  const fetchJobtitles = async () => {
    try {
      setLoading(true);
      const response = await getJobtitles();
      if (response.status) {
        setJobtitles(response.data.items);
        setFilteredJobtitles(response.data.items);
      }
    } catch {
      toast.error('Veriler yüklenirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const loadJobtitles = async () => {
      await fetchJobtitles();
    };
    
    void loadJobtitles();
  }, []);

  const filterAndSearchJobtitles = (searchValue: string, filterValue: string) => {
    let filtered = jobtitles;

    if (filterValue === 'root') {
      filtered = filtered.filter(job => job.parent_id === null);
    } else if (filterValue === 'child') {
      filtered = filtered.filter(job => job.parent_id !== null);
    }

    if (searchValue) {
      filtered = filtered.filter(job =>
        job.name.toLowerCase().includes(searchValue.toLowerCase()) ||
        (job.description && job.description.toLowerCase().includes(searchValue.toLowerCase()))
      );
    }

    setFilteredJobtitles(filtered);
  };

  const handleFilterChange = (value: string) => {
    setSelectedFilter(value);
    filterAndSearchJobtitles(searchText, value);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchText(value);
    filterAndSearchJobtitles(value, selectedFilter);
  };

  const filterOptions = [
    { value: 'all', label: 'Tümü' },
    { value: 'root', label: 'Ana Pozisyonlar' },
    { value: 'child', label: 'Alt Pozisyonlar' },
  ];

  const handleDelete = async (id: number) => {
    setDeletingId(id);
    const res = await deleteJobtitle(id);

    if (res.status) {
      toast.success(res.message);
      await fetchJobtitles();
    } else {
      toast.error(res.message);
    }

    setDeletingId(null);
  };

  const handleEdit = (id: number) => {
    router.push(ROUTES.JOBTITLE.UPDATE(id));
  };

  const buildTreeData = (jobtitles: JobtitleResponse[]): JobtitleTreeNode[] => {
    const jobtitleMap = new Map<number, JobtitleTreeNode>();
    const rootJobtitles: JobtitleTreeNode[] = [];

    jobtitles.forEach(job => {
      jobtitleMap.set(job.id, { ...job });
    });

    jobtitles.forEach(job => {
      const jobtitleNode = jobtitleMap.get(job.id);
      if (!jobtitleNode) return;

      if (job.parent_id === null) {
        rootJobtitles.push(jobtitleNode);
      } else {
        const parent = jobtitleMap.get(job.parent_id);
        if (parent) {
          if (!parent.children) {
            parent.children = [];
          }
          parent.children.push(jobtitleNode);
        }
      }
    });

    return rootJobtitles;
  };

  const treeData = buildTreeData(filteredJobtitles);

  const columns: ColumnsType<JobtitleTreeNode> = [
    {
      title: 'Pozisyon',
      dataIndex: 'name',
      key: 'name',
      className: 'px-6 py-4',
      render: (name: string, record: JobtitleTreeNode) => (
        <div className="flex items-center gap-2">
          <FaUserTie className="text-blue-600" />
          <div>
            <div className="font-medium">{name}</div>
            {record.description && (
              <div className="text-xs text-gray-500">{record.description}</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: 'Tip',
      dataIndex: 'parent_id',
      key: 'type',
      width: 150,
      className: 'px-6 py-4',
      render: (parent_id: number | null) => (
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${
          parent_id === null 
            ? 'bg-purple-100 text-purple-800 border-purple-200' 
            : 'bg-green-100 text-green-800 border-green-200'
        }`}>
          {parent_id === null ? 'Ana Pozisyon' : 'Alt Pozisyon'}
        </span>
      ),
    },
    {
      title: 'İşlemler',
      key: 'actions',
      width: 200,
      className: 'px-6 py-4',
      render: (_value: string, record: JobtitleTreeNode) => (
        <Space size="middle">
          <Button
            type="text"
            size="middle"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
            className="text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-lg transition-all duration-200 border-0 px-4 py-2"
          >
            Güncelle
          </Button>
          <Popconfirm
            title="Pozisyonu Sil"
            description={`"${record.name}" pozisyonunu silmek istediğinizden emin misiniz?`}
            onConfirm={() => handleDelete(record.id)}
            okText="Evet, Sil"
            cancelText="İptal"
            okType="danger"
            placement="topRight"
          >
            <Button
              type="text"
              size="middle"
              danger
              icon={<DeleteOutlined />}
              loading={deletingId === record.id}
              disabled={deletingId === record.id}
              className="text-red-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-all duration-200 border-0 px-4 py-2"
            >
              Sil
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      <KBCard>
        <div className="flex flex-col gap-6 lg:flex-row lg:items-center lg:justify-between px-6">
          <div className="flex flex-1 flex-col gap-4 sm:flex-row">
            <div className="max-w-xs flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <FilterOutlined className="mr-1 text-gray-500" />
                Pozisyon Tipi
              </label>
              <Select
                placeholder="Tip seçiniz"
                value={selectedFilter}
                onChange={handleFilterChange}
                options={filterOptions}
                className="w-full"
                size="middle"
                allowClear
                onClear={() => {
                  setSelectedFilter('all');
                  filterAndSearchJobtitles(searchText, 'all');
                }}
              />
            </div>
            
            <div className="max-w-sm flex-1">
              <label className="mb-2 block text-xs font-medium text-gray-700">
                <SearchOutlined className="mr-1 text-gray-500" />
                Arama
              </label>
              <Input
                placeholder="Pozisyon adı veya açıklama ile ara..."
                value={searchText}
                onChange={handleSearchChange}
                prefix={<SearchOutlined className="text-gray-400" />}
                size="middle"
                allowClear
                className=""
              />
            </div>
          </div>

          <div className="flex items-center gap-6">
            <div className="text-center">
              <div className="text-lg font-semibold text-gray-800">{filteredJobtitles.length}</div>
              <div className="text-xs text-gray-500">Gösterilen</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-600">{jobtitles.length}</div>
              <div className="text-xs text-gray-500">Toplam</div>
            </div>
          </div>
        </div>
      </KBCard>

      <KBCard className="overflow-hidden">
        <Table
          columns={columns}
          dataSource={treeData}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 12,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['8', '12', '20', '50'],
            showTotal: (total, range) =>
              `${range[0]}-${range[1]} arası, toplam ${total} kayıt`,
            size: 'default',
            position: ['bottomCenter'],
            className: 'px-8 py-6',
          }}
          scroll={{ x: 'max-content' }}
          size="large"
          className="-mt-6 modern-table [&_.ant-table-tbody>tr>td]:px-6 [&_.ant-table-tbody>tr>td]:py-4 [&_.ant-table-thead>tr>th]:border-b-2 [&_.ant-table-thead>tr>th]:border-slate-200 [&_.ant-table-thead>tr>th]:bg-slate-50/50 [&_.ant-table-thead>tr>th]:px-6 [&_.ant-table-thead>tr>th]:py-4 [&_.ant-table-thead>tr>th]:font-semibold [&_.ant-table-thead>tr>th]:text-slate-700"
          rowClassName="hover:bg-slate-50/50 transition-colors duration-200"
          expandable={{
            defaultExpandAllRows: true,
            indentSize: 30,
            rowExpandable: (record) => Boolean(record.children && record.children.length > 0),
          }}
        />
      </KBCard>
    </div>
  );
};

export default JobtitleListTable;

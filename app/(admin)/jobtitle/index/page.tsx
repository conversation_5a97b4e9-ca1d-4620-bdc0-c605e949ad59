import React from 'react';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { Button } from 'antd';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import JobtitleListTable from '@/app/(admin)/jobtitle/index/JobtitleListTable';

const JobTitlePage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.JOBTITLE.CREATE}>
            <Button type="primary">Pozisyon Ekle</Button>
          </Link>
        }
        links={[{ title: 'Pozisyonlar' }]}
      />
      <div className="m-4">
        <JobtitleListTable />
      </div>
    </>
  );
};

export default JobTitlePage;

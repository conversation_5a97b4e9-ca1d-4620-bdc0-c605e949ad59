"use client";

import { UsergroupResponse } from '@/types/usergroup.types';
import { FaUsers } from 'react-icons/fa';
import { Button, Space, Popconfirm } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { deleteUsergroup } from '@/app/services/usergroup.service';
import { toast } from 'sonner';
import { ROUTES } from '@/lib/routes';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export const UsergroupItem = ({
  usergroup,
}: {
  usergroup: UsergroupResponse;
}) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDelete = async (id: number) => {
    setIsDeleting(true);
    const res = await deleteUsergroup(id);

    if (res.status) {
      toast.success(res.message);
      router.refresh();
    } else {
      toast.error(res.message);
    }

    setIsDeleting(false);
  };

  const handleUpdate = () => {
    router.push(ROUTES.USERGROUP.UPDATE(usergroup.id));
  };

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <FaUsers className="text-sm text-blue-950" />
          <span className="font-medium min-w-20">{usergroup.name}</span>
          <span className="text-xs text-blue-400 ml-5">{usergroup.description}</span>
        </div>

      </div>
      <Space size="small">
        <Button
          type="text"
          size="small"
          icon={<EditOutlined />}
          onClick={handleUpdate}
          className="text-xs"
        >
          Güncelle
        </Button>
        <Popconfirm
          title="Personel Grubunu Sil"
          description={`"${usergroup.name}" personel grubunu silmek istediğinizden emin misiniz?`}
          onConfirm={() => handleDelete(usergroup.id)}
          okText="Evet, Sil"
          cancelText="İptal"
          okType="danger"
        >
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            loading={isDeleting}
            disabled={isDeleting}
            className="text-xs"
          >
            Sil
          </Button>
        </Popconfirm>
      </Space>
    </div>
  );
};

import React from 'react';
import Link from 'next/link';
import { ROUTES } from '@/lib/routes';
import { Button, Collapse, CollapseProps, Empty } from 'antd';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { getUsergroups } from '@/app/services/usergroup.service';
import { UsergroupItem } from '@/app/(admin)/usergroup/index/UsergroupItem';
import KBCard from '@/components/ui/custom/KBCard';

const UserGroupIndexPage = async () => {
  const response = await getUsergroups();
  const usergroups = response.status ? response.data.items : [];

  const items: CollapseProps['items'] = usergroups.map((usergroup) => ({
    key: usergroup.id.toString(),
    label: (
      <UsergroupItem usergroup={usergroup} />
    ),
    children: (
      <div className="p-2">
        -
      </div>
    ),
  }));

  return (
    <>
      <HeaderBreadcrumb
        content={
          <Link href={ROUTES.USERGROUP.CREATE}>
            <Button type="primary">Personel Grubu Ekle</Button>
          </Link>
        }
        links={[{ title: 'Personel Grubları' }]}
      />
      <div className="m-4">
        {!items.length ? (
          <KBCard>
            <Empty />
          </KBCard>
        ) : (
          <Collapse className="!rounded-xs !bg-white shadow-sm" bordered={false} items={items} />
        )}
      </div>
    </>
  );
};

export default UserGroupIndexPage;

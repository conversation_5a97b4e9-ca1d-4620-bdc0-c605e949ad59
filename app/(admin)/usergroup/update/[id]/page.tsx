import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import { ROUTES } from '@/lib/routes';
import KBCard from '@/components/ui/custom/KBCard';
import UpdateUserGroupForm from '@/components/forms/usergroup/UpdateUserGroupForm';
import { getUsergroupById } from '@/app/services/usergroup.service';
import { notFound } from 'next/navigation';

interface UsergroupUpdatePageProps {
  params: Promise<{
    id: string;
  }>;
}

const UsergroupUpdatePage = async ({ params }: UsergroupUpdatePageProps) => {
  const { id } = await params;
  const usergroupId = parseInt(id);
  
  if (isNaN(usergroupId)) {
    notFound();
  }

  const usergroupResponse = await getUsergroupById(usergroupId);

  if (!usergroupResponse.status || !usergroupResponse.data) {
    notFound();
  }

  const usergroup = usergroupResponse.data;

  return (
    <>
      <HeaderBreadcrumb
        content="Personel Grubu Güncelle"
        links={[
          { title: 'Personel Grubları', link: ROUTES.USERGROUP.INDEX },
          { title: 'Personel Grubu Güncelle' },
        ]}
      />
      <KBCard className="m-4 p-4">
        <UpdateUserGroupForm usergroup={usergroup} />
      </KBCard>
    </>
  );
};

export default UsergroupUpdatePage;

import KBCard from '@/components/ui/custom/KBCard';
import { ROUTES } from '@/lib/routes';
import HeaderBreadcrumb from '@/components/layouts/header/HeaderBreadcrumb';
import CreateUserGroupForm from '@/components/forms/usergroup/CreateUserGroupForm';

const UserGroupPage = () => {
  return (
    <>
      <HeaderBreadcrumb
        content="Personel Grubu Ekle"
        links={[
          { title: 'Personel Grubları', link: ROUTES.USERGROUP.INDEX },
          { title: 'Personel Grubu Ekle' },
        ]}
      />

      <KBCard className="m-4 p-4">
        <CreateUserGroupForm />
      </KBCard>
    </>
  );
};

export default UserGroupPage;

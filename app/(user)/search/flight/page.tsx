// components/FlightSearch.tsx
'use client';

import { useState } from 'react';
import { Button, Card, Checkbox, DatePicker, Form, Radio, Select } from 'antd';
import dayjs from 'dayjs';
import {
  FaBriefcase,
  FaCalendarAlt,
  FaExchangeAlt,
  FaPlane,
  FaPlaneArrival,
  FaPlaneDeparture,
  FaSearch,
  FaSuitcase,
  FaUser,
  FaUserFriends,
} from 'react-icons/fa';

const { RangePicker } = DatePicker;
const { Option } = Select;

const FlightSearch = () => {
  const [form] = Form.useForm();
  const [tripType, setTripType] = useState('OneWay');

  const airports = [
    { code: 'IST', name: 'İstanbul (IST)' },
    { code: 'ESB', name: 'Ankara (ESB)' },
    { code: 'ADB', name: 'İzmir (ADB)' },
    { code: 'SAW', name: '<PERSON><PERSON><PERSON> (SAW)' },
    { code: 'AYT', name: '<PERSON><PERSON><PERSON> (AYT)' },
    { code: 'DLM', name: '<PERSON><PERSON> (DLM)' },
    { code: 'TZX', name: '<PERSON><PERSON><PERSON> (TZX)' },
    { code: 'EZS', name: 'Elazığ (EZS)' },
  ];

  const travelReasons = [
    { value: 'business', label: 'İş Seyahati' },
    { value: 'leisure', label: 'Tatil' },
    { value: 'education', label: 'Eğitim' },
    { value: 'health', label: 'Sağlık' },
    { value: 'family', label: 'Aile Ziyareti' },
  ];

  const cabinClasses = [
    { value: 'economy', label: 'Ekonomi' },
    { value: 'business', label: 'Business' },
    { value: 'first', label: 'First Class' },
  ];

  const users = [
    { value: 'user1', label: 'Ahmet Yılmaz' },
    { value: 'user2', label: 'Ayşe Kaya' },
    { value: 'user3', label: 'Mehmet Demir' },
    { value: 'user4', label: 'Zeynep Çelik' },
  ];

  const handleSearch = (values: any) => {
    console.log('Arama verileri:', {
      ...values,
      tripType,
    });
  };

  const swapLocations = () => {
    const from = form.getFieldValue('departureLocation0');
    const to = form.getFieldValue('arrivalLocation0');
    form.setFieldsValue({
      departureLocation0: to,
      arrivalLocation0: from,
    });
  };

  return (
    <Card
      className="mx-auto max-w-4xl rounded-2xl border-0 shadow-xl"
      bodyStyle={{ padding: '24px' }}
    >
      <Form form={form} layout="vertical" onFinish={handleSearch} className="space-y-4">
        {/* Trip Type Selector */}
        <div className="flex flex-col justify-between lg:flex-row">
          <Form.Item name="tripType" initialValue="OneWay" className="w-full">
            <Radio.Group
              className="flex w-full items-center justify-center"
              value={tripType}
              onChange={(e) => setTripType(e.target.value)}
            >
              <Radio.Button value="OneWay" className="flex items-center gap-2">
                <FaPlaneDeparture />
                <span>Tek Yön</span>
              </Radio.Button>
              <Radio.Button value="RoundTrip" className="flex items-center gap-2">
                <FaExchangeAlt />
                <span>Gidiş-Dönüş</span>
              </Radio.Button>
              <Radio.Button value="MultiCity" className="flex items-center gap-2">
                <FaPlane />
                <span>Çoklu Uçuş</span>
              </Radio.Button>
            </Radio.Group>
          </Form.Item>
        </div>

        {/* Location Selectors */}
        <div className="flex gap-2">
          <div className="w-2/3">
            <div className="flex flex-col md:flex-row md:justify-between">
              <Form.Item
                name="departureLocation0"
                label="Nereden"
                className="w-full md:w-1/2"
                rules={[{ required: true, message: 'Kalkış noktası seçin!' }]}
              >
                <Select
                  placeholder="Şehir ya da havaalanı adı giriniz"
                  className="h-10 w-full"
                  suffixIcon={<FaPlaneDeparture className="text-blue-500" />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {airports.map((airport) => (
                    <Option key={airport.code} value={airport.code}>
                      {airport.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Button
                type="dashed"
                className="mx-1 mt-[29.5px] hidden h-10 w-1/12 min-w-12 items-center justify-center md:flex"
                onClick={swapLocations}
                icon={<FaExchangeAlt />}
              />

              <Form.Item
                name="arrivalLocation0"
                label="Nereye"
                className="w-full md:w-1/2"
                rules={[{ required: true, message: 'Varış noktası seçin!' }]}
              >
                <Select
                  placeholder="Şehir ya da havaalanı adı giriniz"
                  className="h-10 w-full"
                  suffixIcon={<FaPlaneArrival className="text-green-500" />}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {airports.map((airport) => (
                    <Option key={airport.code} value={airport.code}>
                      {airport.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          <Form.Item
            name="date0"
            label="Seyahat Tarihi"
            className="w-full lg:w-1/3"
            rules={[{ required: true, message: 'Tarih seçin!' }]}
          >
            {tripType === 'RoundTrip' ? (
              <RangePicker
                className="h-10 w-full"
                disabledDate={(current) => current && current < dayjs().endOf('day')}
                placeholder={['Kalkış Tarihi', 'Dönüş Tarihi']}
                format="DD/MM/YYYY"
                suffixIcon={<FaCalendarAlt className="text-purple-500" />}
              />
            ) : (
              <DatePicker
                className="h-10 w-full"
                disabledDate={(current) => current && current < dayjs().endOf('day')}
                placeholder="Kalkış Tarihi"
                format="DD/MM/YYYY"
                suffixIcon={<FaCalendarAlt className="text-purple-500" />}
              />
            )}
          </Form.Item>
        </div>

        {/* Additional Options */}
        <div className="flex flex-col gap-2 lg:flex-row">
          <Form.Item
            name="travelReason"
            label="Seyahat Nedeni"
            className="w-full lg:w-1/3"
            rules={[{ required: true, message: 'Seyahat nedeni seçin!' }]}
          >
            <Select
              placeholder="Seyahat nedeni seçiniz"
              className="h-10 w-full"
              suffixIcon={<FaBriefcase className="text-orange-500" />}
            >
              {travelReasons.map((reason) => (
                <Option key={reason.value} value={reason.value}>
                  {reason.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="cabin"
            label="Kabin Sınıfı, Yolcular"
            className="w-full lg:w-1/3"
            initialValue={{ cabin: 'economy', passengers: 1 }}
          >
            <Select
              placeholder="Kabin sınıfı ve yolcu sayısı"
              className="h-10 w-full"
              suffixIcon={<FaUserFriends className="text-blue-500" />}
            >
              <Option value="economy-1">1 Yolcu, Ekonomi</Option>
              <Option value="economy-2">2 Yolcu, Ekonomi</Option>
              <Option value="business-1">1 Yolcu, Business</Option>
              <Option value="business-2">2 Yolcu, Business</Option>
              <Option value="first-1">1 Yolcu, First Class</Option>
              <Option value="first-2">2 Yolcu, First Class</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="selectedUser"
            label="Kullanıcı Seçiniz"
            className="w-full lg:w-1/3"
            rules={[{ required: true, message: 'Kullanıcı seçin!' }]}
          >
            <Select
              placeholder="Kullanıcı Seçiniz"
              className="h-10 w-full"
              suffixIcon={<FaUser className="text-green-500" />}
              showSearch
              filterOption={(input, option) =>
                (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {users.map((user) => (
                <Option key={user.value} value={user.value}>
                  {user.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        {/* Checkboxes and Search Button */}
        <div className="mt-2 -mb-5 flex flex-col justify-between lg:flex-row" id="focusable-div">
          <div className="-mt-3 flex gap-4">
            <Form.Item name="directFlightsOnly" valuePropName="checked" className="mt-4">
              <Checkbox>
                <span className="flex items-center">
                  <FaPlane className="mr-2 text-blue-500" />
                  Sadece direkt uçuşlar
                </span>
              </Checkbox>
            </Form.Item>

            <Form.Item name="includeNonBaggageFlights" valuePropName="checked" className="mt-4">
              <Checkbox defaultChecked>
                <span className="flex items-center">
                  <FaSuitcase className="mr-2 text-orange-500" />
                  Bagajsız uçuşları da göster
                </span>
              </Checkbox>
            </Form.Item>
          </div>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<FaSearch />}
              className="flex h-10 w-28 items-center justify-center border-0 bg-gradient-to-r from-blue-600 to-indigo-700 shadow-md hover:from-blue-700 hover:to-indigo-800"
            >
              Ara
            </Button>
          </Form.Item>
        </div>
      </Form>
    </Card>
  );
};

export default FlightSearch;

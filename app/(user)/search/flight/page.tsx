'use client';

import { useState } from 'react';
import { But<PERSON>, Card, DatePicker, Form, Input, Select } from 'antd';
import dayjs from 'dayjs';
import {
  FaCalendarAlt,
  FaExchangeAlt,
  FaPlaneArrival,
  FaPlaneDeparture,
  FaSearch,
  FaUser,
} from 'react-icons/fa';

const { RangePicker } = DatePicker;
const { Option } = Select;

const FlightSearchForm = () => {
  const [form] = Form.useForm();
  const [tripType, setTripType] = useState('roundtrip');

  const airports = [
    { code: 'IST', name: 'İstanbul (IST)' },
    { code: 'ESB', name: 'Ankara (ESB)' },
    { code: 'ADB', name: 'İz<PERSON> (ADB)' },
    { code: 'SAW', name: '<PERSON><PERSON><PERSON> (SAW)' },
    { code: 'AYT', name: '<PERSON><PERSON><PERSON> (AYT)' },
    { code: 'DLM', name: '<PERSON><PERSON> (DLM)' },
  ];

  const handleSearch = (values: any) => {
    console.log('Arama verileri:', {
      ...values,
      departureDate: values.dates[0],
      returnDate: tripType === 'roundtrip' ? values.dates[1] : null,
    });
  };

  return (
    <Card
      className="mx-auto max-w-4xl rounded-2xl border-0 shadow-xl"
    >
      <Form form={form} layout="vertical" onFinish={handleSearch} className="space-y-6">
        {/* Trip Type Selector */}
        <div className="mb-8 flex space-x-4">
          <Button
            type={tripType === 'roundtrip' ? 'primary' : 'default'}
            onClick={() => setTripType('roundtrip')}
            className="h-12 flex-1 text-base font-medium"
            icon={<FaExchangeAlt className="mr-2" />}
          >
            Gidiş-Dönüş
          </Button>
          <Button
            type={tripType === 'oneway' ? 'primary' : 'default'}
            onClick={() => setTripType('oneway')}
            className="h-12 flex-1 text-base font-medium"
            icon={<FaPlaneDeparture className="mr-2" />}
          >
            Tek Yön
          </Button>
        </div>

        {/* From/To Inputs */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item
            name="from"
            label={
              <span className="flex items-center font-medium text-gray-700">
                <FaPlaneDeparture className="mr-2 text-blue-500" />
                Nereden
              </span>
            }
            rules={[{ required: true, message: 'Lütfen kalkış noktası seçin!' }]}
          >
            <Select
              placeholder="Kalkış havalimanı seçin"
              className="h-12"
              suffixIcon={<FaPlaneDeparture className="text-blue-500" />}
            >
              {airports.map((airport) => (
                <Option key={airport.code} value={airport.code}>
                  {airport.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="to"
            label={
              <span className="flex items-center font-medium text-gray-700">
                <FaPlaneArrival className="mr-2 text-green-500" />
                Nereye
              </span>
            }
            rules={[{ required: true, message: 'Lütfen varış noktası seçin!' }]}
          >
            <Select
              placeholder="Varış havalimanı seçin"
              className="h-12"
              suffixIcon={<FaPlaneArrival className="text-green-500" />}
            >
              {airports.map((airport) => (
                <Option key={airport.code} value={airport.code}>
                  {airport.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        {/* Date Picker */}
        <Form.Item
          name="dates"
          label={
            <span className="flex items-center font-medium text-gray-700">
              <FaCalendarAlt className="mr-2 text-purple-500" />
              Tarihler
            </span>
          }
          rules={[{ required: true, message: 'Lütfen tarih seçin!' }]}
        >
          <RangePicker
            className="h-12 w-full"
            disabledDate={(current) => current && current < dayjs().endOf('day')}
            placeholder={['Gidiş Tarihi', tripType === 'roundtrip' ? 'Dönüş Tarihi' : '']}
            format="DD/MM/YYYY"
            suffixIcon={<FaCalendarAlt className="text-purple-500" />}
          />
        </Form.Item>

        {/* Passengers & Class */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <Form.Item
            name="passengers"
            label={
              <span className="flex items-center font-medium text-gray-700">
                <FaUser className="mr-2 text-orange-500" />
                Yolcular
              </span>
            }
            initialValue={1}
          >
            <Input
              type="number"
              min={1}
              max={9}
              className="h-12"
              addonAfter={<FaUser className="text-orange-500" />}
            />
          </Form.Item>

          <Form.Item name="class" label="Sınıf" initialValue="economy">
            <Select
              placeholder="Uçuş sınıfı"
              className="h-12"
              suffixIcon={<FaPlaneDeparture className="text-blue-500" />}
            >
              <Option value="economy">Ekonomi</Option>
              <Option value="business">Business</Option>
              <Option value="first">First Class</Option>
            </Select>
          </Form.Item>
        </div>

        {/* Search Button */}
        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<FaSearch />}
            size="large"
            className="h-14 w-full border-0 bg-gradient-to-r from-blue-600 to-indigo-700 text-lg font-semibold shadow-lg hover:from-blue-700 hover:to-indigo-800"
          >
            Uçuşları Ara
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default FlightSearchForm;

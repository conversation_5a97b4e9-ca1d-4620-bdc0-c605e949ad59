import React from 'react';

const Loading = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
      <div className="relative">
        <div className="w-16 h-16 rounded-full border-4 border-gray-200 border-t-blue-600 animate-spin"></div>
        <div className="absolute inset-2 w-12 h-12 rounded-full border-2 border-gray-100 border-t-pink-500 animate-spin [animation-direction:reverse] [animation-duration:0.8s]"></div>
      </div>
    </div>
  );
};

export default Loading;

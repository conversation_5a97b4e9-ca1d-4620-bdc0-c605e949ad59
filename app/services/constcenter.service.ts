'use server';

import { CostCenterCreateRequest, CostCenterResponse, CostCenterUpdateRequest } from '@/types/costcenter.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getCostCenters() {
  return await HttpService.get<ApiResponseCollection<CostCenterResponse>>('/cost-center');
}

export async function getCostCenterById(id: number) {
  return await HttpService.get<ApiResponseResource<CostCenterResponse>>(`/cost-center/${id}`);
}

export async function createCostCenter(payload: CostCenterCreateRequest) {
  console.log('Service payload:', payload);
  return await HttpService.post<ApiResponseResource<CostCenterResponse>, CostCenterCreateRequest>(
    '/cost-center',
    payload,
  );
}

export async function updateCostCenter(id: number, payload: CostCenterUpdateRequest) {
  return await HttpService.put<ApiResponseResource<CostCenterResponse>, CostCenterUpdateRequest>(
    `/cost-center/${id}`,
    payload,
  );
}

export async function deleteCostCenter(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/cost-center/${id}`);
}

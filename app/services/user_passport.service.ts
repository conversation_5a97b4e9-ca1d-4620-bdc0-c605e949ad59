'use server';

import {
  UserPassportCreateRequest,
  UserPassportResponse,
  UserPassportUpdateRequest,
} from '@/types/user_passport.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserPassports() {
  return await HttpService.get<ApiResponseCollection<UserPassportResponse>>('/user-passport');
}

export async function getUserPassportById(id: number) {
  return await HttpService.get<ApiResponseResource<UserPassportResponse>>(`/user-passport/${id}`);
}

export async function createUserPassport(payload: UserPassportCreateRequest) {
  return await HttpService.post<
    ApiResponseResource<UserPassportResponse>,
    UserPassportCreateRequest
  >('/user-passport', payload);
}

export async function updateUserPassport(id: number, payload: UserPassportUpdateRequest) {
  return await HttpService.put<
    ApiResponseResource<UserPassportResponse>,
    UserPassportUpdateRequest
  >(`/user-passport/${id}`, payload);
}

export async function deleteUserPassport(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-passport/${id}`);
}

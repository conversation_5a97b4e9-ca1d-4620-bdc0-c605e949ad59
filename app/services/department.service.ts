'use server';

import {
  DepartmentCreateRequest,
  DepartmentResponse,
  DepartmentUpdateRequest,
} from '@/types/department.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getDepartments() {
  return await HttpService.get<ApiResponseCollection<DepartmentResponse>>('/department');
}

export async function getDepartmentById(id: number) {
  return await HttpService.get<ApiResponseResource<DepartmentResponse>>(`/department/${id}`);
}

export async function createDepartment(payload: DepartmentCreateRequest) {
  const data = {
    ...payload,
    parent_id: payload.parent_id === 0 ? null : payload.parent_id,
  };

  return await HttpService.post<ApiResponseResource<DepartmentResponse>, DepartmentCreateRequest>(
    '/department',
    data,
  );
}

export async function updateDepartment(id: number, payload: DepartmentUpdateRequest) {
  const data = {
    ...payload,
    parent_id: payload.parent_id === 0 ? null : payload.parent_id,
  };

  return await HttpService.put<ApiResponseResource<DepartmentResponse>, DepartmentUpdateRequest>(
    `/department/${id}`,
    data,
  );
}

export async function deleteDepartment(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/department/${id}`);
}

'use server';

import { BranchCreateRequest, BranchResponse, BranchUpdateRequest } from '@/types/branch.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getBranches() {
  return await HttpService.get<ApiResponseCollection<BranchResponse>>('/branch');
}

export async function getBranchById(id: number) {
  return await HttpService.get<ApiResponseResource<BranchResponse>>(`/branch/${id}`);
}

export async function createBranch(payload: BranchCreateRequest) {
  return await HttpService.post<ApiResponseResource<BranchResponse>, BranchCreateRequest>(
    '/branch',
    payload,
  );
}

export async function updateBranch(id: number, payload: BranchUpdateRequest) {
  return await HttpService.put<ApiResponseResource<BranchResponse>, BranchUpdateRequest>(
    `/branch/${id}`,
    payload,
  );
}

export async function deleteBranch(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/branch/${id}`);
}

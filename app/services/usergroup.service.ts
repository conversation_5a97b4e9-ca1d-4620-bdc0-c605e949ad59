'use server';

import {
  UsergroupCreateRequest,
  UsergroupResponse,
  UsergroupUpdateRequest,
} from '@/types/usergroup.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUsergroups() {
  return await HttpService.get<ApiResponseCollection<UsergroupResponse>>('/user-group');
}

export async function getUsergroupById(id: number) {
  return await HttpService.get<ApiResponseResource<UsergroupResponse>>(`/user-group/${id}`);
}

export async function createUsergroup(payload: UsergroupCreateRequest) {
  return await HttpService.post<ApiResponseResource<UsergroupResponse>, UsergroupCreateRequest>(
    '/user-group',
    payload,
  );
}

export async function updateUsergroup(id: number, payload: UsergroupUpdateRequest) {
  return await HttpService.put<ApiResponseResource<UsergroupResponse>, UsergroupUpdateRequest>(
    `/user-group/${id}`,
    payload,
  );
}

export async function deleteUsergroup(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-group/${id}`);
}

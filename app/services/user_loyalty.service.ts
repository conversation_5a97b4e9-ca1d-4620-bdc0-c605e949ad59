'use server';

import {
  UserLoyaltyCreateRequest,
  UserLoyaltyResponse,
  UserLoyaltyUpdateRequest,
} from '@/types/user_loyalty.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserLoyalties() {
  return await HttpService.get<ApiResponseCollection<UserLoyaltyResponse>>('/user-loyalty');
}

export async function getUserLoyaltyById(id: number) {
  return await HttpService.get<ApiResponseResource<UserLoyaltyResponse>>(`/user-loyalty/${id}`);
}

export async function createUserLoyalty(payload: UserLoyaltyCreateRequest) {
  return await HttpService.post<ApiResponseResource<UserLoyaltyResponse>, UserLoyaltyCreateRequest>(
    '/user-loyalty',
    payload,
  );
}

export async function updateUserLoyalty(id: number, payload: UserLoyaltyUpdateRequest) {
  return await HttpService.put<ApiResponseResource<UserLoyaltyResponse>, UserLoyaltyUpdateRequest>(
    `/user-loyalty/${id}`,
    payload,
  );
}

export async function deleteUserLoyalty(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-loyalty/${id}`);
}

'use server';

import { ReasonCreateRequest, ReasonResponse, ReasonUpdateRequest } from '@/types/reason.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getReasons(type?: string) {
  const url = type ? `/reason?type=${type}` : '/reason';
  return await HttpService.get<ApiResponseCollection<ReasonResponse>>(url);
}

export async function getReasonById(id: number) {
  return await HttpService.get<ApiResponseResource<ReasonResponse>>(`/reason/${id}`);
}

export async function createReason(payload: ReasonCreateRequest) {
  return await HttpService.post<ApiResponseResource<ReasonResponse>, ReasonCreateRequest>(
    '/reason',
    payload,
  );
}

export async function updateReason(id: number, payload: ReasonUpdateRequest) {
  return await HttpService.put<ApiResponseResource<ReasonResponse>, ReasonUpdateRequest>(
    `/reason/${id}`,
    payload,
  );
}

export async function deleteReason(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/reason/${id}`);
}

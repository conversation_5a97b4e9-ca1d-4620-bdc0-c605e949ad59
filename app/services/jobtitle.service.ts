'use server';

import {
  JobtitleCreateRequest,
  JobtitleResponse,
  JobtitleUpdateRequest,
} from '@/types/jobtitle.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getJobtitles() {
  return await HttpService.get<ApiResponseCollection<JobtitleResponse>>('/job-title');
}

export async function getJobtitleById(id: number) {
  return await HttpService.get<ApiResponseResource<JobtitleResponse>>(`/job-title/${id}`);
}

export async function createJobtitle(payload: JobtitleCreateRequest) {
  const data = {
    ...payload,
    parent_id: payload.parent_id === 0 ? null : payload.parent_id,
  };

  return await HttpService.post<ApiResponseResource<JobtitleResponse>, JobtitleCreateRequest>(
    '/job-title',
    data,
  );
}

export async function updateJobtitle(id: number, payload: JobtitleUpdateRequest) {
  const data = {
    ...payload,
    parent_id: payload.parent_id === 0 ? null : payload.parent_id,
  };

  return await HttpService.put<ApiResponseResource<JobtitleResponse>, JobtitleUpdateRequest>(
    `/job-title/${id}`,
    data,
  );
}

export async function deleteJobtitle(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/job-title/${id}`);
}

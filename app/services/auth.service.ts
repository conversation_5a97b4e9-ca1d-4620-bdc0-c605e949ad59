'use server';

import { HttpService } from '@/lib/http';
import { ApiResponseResource } from '@/types/base.types';
import {
  LoginFormRequest,
  LoginFormResponse,
  RegisterFormRequest,
  RegisterFormResponse,
} from '@/types/auth.types';
import { cookies } from 'next/headers';
import { ROUTES } from '@/lib/routes';
import { UserResponse } from '@/types/user.types';

export async function registerWithOrganization(payload: RegisterFormRequest) {
  const data = {
    ...payload,
    parent_id: payload.parent_id ? payload.parent_id : null,
  };

  console.log(data, 'data');
  return await HttpService.post<ApiResponseResource<RegisterFormResponse>>(ROUTES.AUTH.REGISTER, data);
}

export async function loginWithEmail(payload: LoginFormRequest) {
  const data = {
    ...payload,
  };

  const res = await HttpService.post<ApiResponseResource<LoginFormResponse>>(ROUTES.AUTH.LOGIN, data);
  if (res.status) {
    (await cookies()).set({
      name: 'token',
      value: res.data.token,
      path: '/',
    });
  }

  return res;
}

export async function getAuthUser() {
  return await HttpService.get<ApiResponseResource<UserResponse>>(ROUTES.AUTH.AUTH_USER);
}

export async function logout() {
  (await cookies()).delete('token');
  return await HttpService.post<ApiResponseResource<void>>(ROUTES.AUTH.LOGOUT, {});
}

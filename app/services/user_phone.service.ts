'use server';

import {
  UserPhoneCreateRequest,
  UserPhoneResponse,
  UserPhoneUpdateRequest,
} from '@/types/user_phone.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserPhones() {
  return await HttpService.get<ApiResponseCollection<UserPhoneResponse>>('/user-phone');
}

export async function getUserPhoneById(id: number) {
  return await HttpService.get<ApiResponseResource<UserPhoneResponse>>(`/user-phone/${id}`);
}

export async function createUserPhone(payload: UserPhoneCreateRequest) {
  return await HttpService.post<ApiResponseResource<UserPhoneResponse>, UserPhoneCreateRequest>(
    '/user-phone',
    payload,
  );
}

export async function updateUserPhone(id: number, payload: UserPhoneUpdateRequest) {
  return await HttpService.put<ApiResponseResource<UserPhoneResponse>, UserPhoneUpdateRequest>(
    `/user-phone/${id}`,
    payload,
  );
}

export async function deleteUserPhone(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-phone/${id}`);
}

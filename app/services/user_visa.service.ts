'use server';

import {
  UserVisaCreateRequest,
  UserVisaResponse,
  UserVisaUpdateRequest,
} from '@/types/user_visa.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserVisas() {
  return await HttpService.get<ApiResponseCollection<UserVisaResponse>>('/user-visa');
}

export async function getUserVisaById(id: number) {
  return await HttpService.get<ApiResponseResource<UserVisaResponse>>(`/user-visa/${id}`);
}

export async function createUserVisa(payload: UserVisaCreateRequest) {
  return await HttpService.post<ApiResponseResource<UserVisaResponse>, UserVisaCreateRequest>(
    '/user-visa',
    payload,
  );
}

export async function updateUserVisa(id: number, payload: UserVisaUpdateRequest) {
  return await HttpService.put<ApiResponseResource<UserVisaResponse>, UserVisaUpdateRequest>(
    `/user-visa/${id}`,
    payload,
  );
}

export async function deleteUserVisa(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-visa/${id}`);
}

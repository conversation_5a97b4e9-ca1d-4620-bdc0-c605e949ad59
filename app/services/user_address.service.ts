'use server';

import { UserAddressCreateRequest, UserAddressResponse } from '@/types/user_address.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserAddresses() {
  return await HttpService.get<ApiResponseCollection<UserAddressResponse>>('/user-address');
}

export async function getUserAddressById(id: number) {
  return await HttpService.get<ApiResponseResource<UserAddressResponse>>(`/user-address/${id}`);
}

export async function createUserAddress(payload: UserAddressCreateRequest) {
  return await HttpService.post<ApiResponseResource<UserAddressResponse>, UserAddressCreateRequest>(
    '/user-address',
    payload,
  );
}

export async function updateUserAddress(id: number, payload: UserAddressCreateRequest) {
  return await HttpService.put<ApiResponseResource<UserAddressResponse>, UserAddressCreateRequest>(
    `/user-address/${id}`,
    payload,
  );
}

export async function deleteUserAddress(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-address/${id}`);
}

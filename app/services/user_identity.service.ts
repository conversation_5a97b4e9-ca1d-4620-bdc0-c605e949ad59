'use server';

import {
  UserIdentityCreateRequest,
  UserIdentityResponse,
  UserIdentityUpdateRequest,
} from '@/types/user_identity.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUserIdentities() {
  return await HttpService.get<ApiResponseCollection<UserIdentityResponse>>('/user-identity');
}

export async function getUserIdentityById(id: number) {
  return await HttpService.get<ApiResponseResource<UserIdentityResponse>>(`/user-identity/${id}`);
}

export async function createUserIdentity(payload: UserIdentityCreateRequest) {
  return await HttpService.post<
    ApiResponseResource<UserIdentityResponse>,
    UserIdentityCreateRequest
  >('/user-identity', payload);
}

export async function updateUserIdentity(id: number, payload: UserIdentityUpdateRequest) {
  return await HttpService.put<
    ApiResponseResource<UserIdentityResponse>,
    UserIdentityUpdateRequest
  >(`/user-identity/${id}`, payload);
}

export async function deleteUserIdentity(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user-identity/${id}`);
}

'use server';

import { UserCreateRequest, UserResponse, UserUpdateRequest } from '@/types/user.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getUsers() {
  return await HttpService.get<ApiResponseCollection<UserResponse>>('/user');
}

export async function getUserById(id: number) {
  return await HttpService.get<ApiResponseResource<UserResponse>>(`/user/${id}`);
}

export async function createUser(values: UserCreateRequest) {
  return await HttpService.post<ApiResponseResource<UserResponse>, UserCreateRequest>(
    '/user',
    values,
  );
}

export async function updateUser(id: number, values: UserUpdateRequest) {
  return await HttpService.put<ApiResponseResource<UserResponse>, UserUpdateRequest>(
    `/user/${id}`,
    values,
  );
}

export async function deleteUser(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/user/${id}`);
}

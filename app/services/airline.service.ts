'use server';

import { AirlineResponse } from '@/types/airline.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection } from '@/types/base.types';

export async function getAirlines(nextPage: string = '') {
  return await HttpService.get<ApiResponseCollection<AirlineResponse>>(`/airlines${nextPage}`);
}

export async function searchAirlines(searchTerm: string, nextPage: string = '') {
  const searchParam = searchTerm ? `/${encodeURIComponent(searchTerm)}` : '';
  return await HttpService.get<ApiResponseCollection<AirlineResponse>>(
    `/airlines${searchParam}${nextPage}`,
  );
}

'use server';

import { GuestCreateRequest, GuestResponse, GuestUpdateRequest } from '@/types/guest.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getGuests() {
  return await HttpService.get<ApiResponseCollection<GuestResponse>>('/guest');
}

export async function getGuestById(id: number) {
  return await HttpService.get<ApiResponseResource<GuestResponse>>(`/guest/${id}`);
}

export async function createGuest(payload: GuestCreateRequest) {
  return await HttpService.post<ApiResponseResource<GuestResponse>, GuestCreateRequest>(
    '/guest',
    payload,
  );
}

export async function updateGuest(id: number, payload: GuestUpdateRequest) {
  return await HttpService.put<ApiResponseResource<GuestResponse>, GuestUpdateRequest>(
    `/guest/${id}`,
    payload,
  );
}

export async function deleteGuest(id: number) {
  return await HttpService.delete<ApiResponseResource<void>>(`/guest/${id}`);
}

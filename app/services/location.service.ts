'use server';

import { CityResponse, CountryResponse, StateResponse } from '@/types/location.types';
import { HttpService } from '@/lib/http';
import { ApiResponseCollection, ApiResponseResource } from '@/types/base.types';

export async function getCountries(nextPage: string = '') {
  return await HttpService.get<ApiResponseCollection<CountryResponse>>(
    `/locations/countries${nextPage}`,
  );
}

export async function getCountryById(id: number) {
  return await HttpService.get<ApiResponseResource<CountryResponse>>(
    `/locations/countries?id=${id}`,
  );
}

export async function searchCountries(searchTerm: string, nextPage: string = '') {
  const searchParam = searchTerm ? `/search/${encodeURIComponent(searchTerm)}` : '';
  return await HttpService.get<ApiResponseCollection<CountryResponse>>(
    `/locations/countries${searchParam}${nextPage}`,
  );
}

export async function getStates(nextPage: string = '') {
  return await HttpService.get<ApiResponseCollection<StateResponse>>(
    `/locations/states${nextPage}`,
  );
}

export async function getStateById(id: number) {
  return await HttpService.get<ApiResponseResource<StateResponse>>(`/locations/states?id=${id}`);
}

export async function searchStates(searchTerm: string, nextPage: string = '') {
  const searchParam = searchTerm ? `/search/${encodeURIComponent(searchTerm)}` : '';
  return await HttpService.get<ApiResponseCollection<StateResponse>>(
    `/locations/states${searchParam}${nextPage}`,
  );
}

export async function getCities(nextPage: string = '') {
  return await HttpService.get<ApiResponseCollection<CityResponse>>(`/locations/cities${nextPage}`);
}

export async function getCityById(id: number) {
  return await HttpService.get<ApiResponseResource<CityResponse>>(`/locations/cities?id=${id}`);
}

export async function searchCities(searchTerm: string, nextPage: string = '') {
  const searchParam = searchTerm ? `/search/${encodeURIComponent(searchTerm)}` : '';
  return await HttpService.get<ApiResponseCollection<CityResponse>>(
    `/locations/cities${searchParam}${nextPage}`,
  );
}

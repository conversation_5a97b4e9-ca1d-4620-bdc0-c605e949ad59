import RegisterForm from '@/components/forms/auth/RegisterForm';
import { getOrganizations } from '@/app/services/organization.service';

const RegisterPage = async () => {
  const organizations = await getOrganizations();

  console.log(organizations, 'organizations');

  return (
      <>
        <RegisterForm organizations={organizations.status ? organizations.data.items : []} />
      </>
  );
};

export default RegisterPage;

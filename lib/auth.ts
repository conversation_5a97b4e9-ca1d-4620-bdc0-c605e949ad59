import { type JWTPayload, jwtVerify } from 'jose';
import { ROUTES } from '@/lib/routes';

export const AUTH_PAGES = ['/', ROUTES.AUTH.LOGIN, ROUTES.AUTH.REGISTER] as const;

export const getJwtSecretKey = (): Uint8Array => {
  const secret = process.env.JWT_SECRET_KEY;
  if (!secret) throw new Error('JWT_SECRET_KEY is not defined');
  return new TextEncoder().encode(secret);
};

export const verifyJwtToken = async (token: string): Promise<JWTPayload | null> => {
  try {
    const { payload } = await jwtVerify(token, getJwtSecretKey());
    return payload;
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
  } catch (_error) {
    return null;
  }
};

export const isAuthPages = (url: string): boolean => {
  if (url === '/') return true;
  return AUTH_PAGES.some((page) => url === page);
};

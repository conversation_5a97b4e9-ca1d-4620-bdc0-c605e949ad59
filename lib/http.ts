import { cookies } from 'next/headers';

const BASE_URL = process.env.API_URL;

interface FetchOptions<TBody = unknown> extends Omit<RequestInit, 'body'> {
  body?: TBody;
}

async function request<TResponse = unknown, TBody = unknown>(
  endpoint: string,
  options: FetchOptions<TBody> = {},
): Promise<TResponse> {
  const token = (await cookies()).get('token')?.value ?? null;
  const {
    method = 'GET',
    headers = {
      Authorization: `Bearer ${token}`,
    },
    body,
  } = options;

  const finalHeaders: HeadersInit = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    ...headers,
  };

  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      method,
      headers: finalHeaders,
      cache: 'no-store',
      body: body ? JSON.stringify(body) : undefined,
    });

    if (response.status === 401) {
      (await cookies()).delete('token');

      return {
        status: false,
        message: 'Oturum süresi dolmuş',
      } as TResponse;
    }

    if (response.status === 404) {
      return {
        success: false,
        message: '<PERSON><PERSON> bulunamadı',
      } as TResponse;
    }

    return await response.json();
  } catch (e) {
    return {
      success: false,
      message: e instanceof Error ? e.message : 'Sunucu hatası oluştu. FAILED',
    } as TResponse;
  }
}

export const HttpService = {
  get: <TResponse>(url: string, options?: FetchOptions) =>
    request<TResponse>(url, { ...options, method: 'GET' }),

  post: <TResponse, TBody = unknown>(url: string, body: TBody, options?: FetchOptions<TBody>) =>
    request<TResponse, TBody>(url, { ...options, method: 'POST', body }),

  put: <TResponse, TBody = unknown>(url: string, body: TBody, options?: FetchOptions<TBody>) =>
    request<TResponse, TBody>(url, { ...options, method: 'PUT', body }),

  delete: <TResponse>(url: string, options?: FetchOptions) =>
    request<TResponse>(url, { ...options, method: 'DELETE' }),
};

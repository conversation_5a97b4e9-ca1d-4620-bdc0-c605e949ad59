import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// TW Merge
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Mask number with *
export const utlMaskNumber = (number: string, startLength = 3, endLength = 3) => {
  if (!number) return '';
  if (number.length <= startLength + endLength) return number;
  const start = number.slice(0, startLength);
  const end = number.slice(-endLength);
  const middle = '*'.repeat(number.length - startLength - endLength);
  return `${start}${middle}${end}`;
};

import { NextRequest, NextResponse } from 'next/server';
import { isAuthPages, verifyJwtToken } from '@/lib/auth';
import { ROUTES } from '@/lib/routes';

export async function middleware(request: NextRequest) {
  const { url, nextUrl, cookies } = request;
  const token = cookies.get('token')?.value ?? null;

  const hasVerifyToken = token && (await verifyJwtToken(token));

  if (token && !hasVerifyToken) {
    const response = NextResponse.redirect(new URL(ROUTES.AUTH.LOGIN, url));
    response.cookies.delete('token');
    return response;
  }
  const isAuthPage = isAuthPages(nextUrl.pathname);

  if (isAuthPage) {
    if (!hasVerifyToken) {
      return NextResponse.next();
    } else {
      return NextResponse.redirect(new URL(ROUTES.DASHBOARD, url));
    }
  }

  if (!hasVerifyToken) {
    const searchParams = new URLSearchParams(nextUrl.searchParams);
    searchParams.set('redirect', nextUrl.pathname);
    return NextResponse.redirect(new URL(`${ROUTES.AUTH.LOGIN}?${searchParams.toString()}`, url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};

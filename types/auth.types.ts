export type LoginFormRequest = {
  email: string;
  password: string;
};

export type LoginFormResponse = {
  user: {
    id: number;
    name: string;
    email: string;
  };
  token: string;
};

export type RegisterFormRequest = {
  organization_type: number;
  parent_id?: number;
  organization_name: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  password_confirmation: string;
};

export type RegisterFormResponse = {
  user: {
    id: number;
    name: string;
    email: string;
  };
  token: string;
};

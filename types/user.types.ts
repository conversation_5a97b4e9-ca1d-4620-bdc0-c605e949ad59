import { DepartmentResponse } from './department.types';
import { JobtitleResponse } from './jobtitle.types';
import { UsergroupResponse } from './usergroup.types';
import { UserIdentityResponse } from '@/types/user_identity.types';
import { UserAddressResponse } from '@/types/user_address.types';
import { UserPhoneResponse } from '@/types/user_phone.types';
import { UserPassportResponse } from '@/types/user_passport.types';
import { UserVisaResponse } from '@/types/user_visa.types';
import { UserLoyaltyResponse } from '@/types/user_loyalty.types';

export type UserResponse = {
  id: number;
  organization_id?: number;
  first_name: string;
  last_name: string;
  email: string;
  status: string;
  department: DepartmentResponse | null;
  job_title: JobtitleResponse | null;
  user_group: UsergroupResponse | null;
  parent_user: UserResponse | null;
  identities: UserIdentityResponse[];
  adresses: UserAddressResponse[];
  phones: UserPhoneResponse[];
  passports: UserPassportResponse[];
  visas: UserVisaResponse[];
  loyalties: UserLoyaltyResponse[];
  email_verified_at?: Date | null;
  created_at: string;
  updated_at: string;
};

export type UserCreateRequest = {
  first_name: string;
  last_name: string;
  email: string;
  status: string;
  department_id: number | null;
  jobtitle_id: number | null;
  parent_user_id: number | null;
  usergroup_id: number | null;
};

export type UserUpdateRequest = UserCreateRequest;

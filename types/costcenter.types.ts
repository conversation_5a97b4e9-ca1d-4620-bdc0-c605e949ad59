import { DepartmentResponse } from './department.types';
import { JobtitleResponse } from './jobtitle.types';

export type CostCenterResponse = {
  id: number;
  name: string;
  code: string;
  organization_id?: number;
  departments: DepartmentResponse[];
  jobtitles: JobtitleResponse[];
  created_at: string;
  updated_at: string;
};

export type CostCenterCreateRequest = {
  name: string;
  code: string;
  department_ids: number[];
  jobtitle_ids: number[];
};

export type CostCenterUpdateRequest = CostCenterCreateRequest;

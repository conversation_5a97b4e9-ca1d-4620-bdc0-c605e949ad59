export type CountryResponse = {
  id: number;
  name: string;
  iso3: string;
  iso2: string;
  numeric_code: string;
  phone_code: string;
  capital: string;
  currency: string;
  currency_name: string;
  currency_symbol: string;
  tld: string;
  native: string;
  subregion: string;
  subregion_id: number;
  nationality: string;
  timezones: string | null;
  latitude: string;
  longitude: string;
  emoji: string;
  emoji_u: string;
  created_at: string;
  updated_at: string;
};

export type StateResponse = {
  id: number;
  name: string;
  country_id: number;
  country_code: string;
  country_name: string;
  state_code: string;
  latitude: string;
  longitude: string;
  created_at: string;
  updated_at: string;
};

export type CityResponse = {
  id: number;
  name: string;
  state_id: number;
  state_code: string;
  state_name: string;
  country_id: number;
  country_code: string;
  country_name: string;
  latitude: string;
  longitude: string;
  wikidata_id: string;
  created_at: string;
  updated_at: string;
};

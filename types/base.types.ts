export type PaginationMeta = {
  next_page: string | null;
  previous_page: string | null;
  has_more_pages: boolean;
  per_page: number;
};

export type SingleResponse<T> = {
  status: true;
  message: string;
  data: T;
};

export type PaginatedResponse<T> = {
  status: true;
  message: string;
  data: {
    items: T[];
    meta: PaginationMeta;
  };
};

export type FailedResponse = {
  status: false;
  message: string;
  errors?: Record<string, string[]>;
};

export type ApiResponseResource<T> = SingleResponse<T> | FailedResponse;
export type ApiResponseCollection<T> = PaginatedResponse<T> | FailedResponse;

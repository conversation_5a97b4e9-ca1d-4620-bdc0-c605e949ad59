export type BranchResponse = {
  id: number;
  address_name: string;
  address_type: string;
  address_1: string;
  address_2?: string;
  accounting_number?: string;
  country: string;
  city?: string;
  neighborhood?: string;
  postal_code?: string;
  tax_number?: string;
  tax_office?: string;
  mersis_no?: string;
  trade_registry_no?: string;
  country_code_phone?: string;
  phone_number?: string;
  country_code_fax?: string;
  fax_number?: string;
  organization_id: number;
  created_at: string;
  updated_at: string;
};

export type BranchCreateRequest = Omit<
  BranchResponse,
  'id' | 'organization_id' | 'created_at' | 'updated_at'
>;

export type BranchUpdateRequest = BranchCreateRequest;

import { Dayjs } from 'dayjs';

export type UserVisaResponse = {
  id: number;
  user_id: number;
  visa_type: 'tourist' | 'business';
  entry_type: 'single' | 'multiple';
  country: string;
  document_number: string;
  passport_number: string;
  start_date: string;
  end_date: string;
  duration: string;
  issued_place: string;
  issue_date: string;
  is_default: boolean;
};

export type UserVisaCreateRequest = Omit<UserVisaResponse, 'id' | 'user_id'>;

export type UserVisaUpdateRequest = UserVisaCreateRequest;

export type UserVisaFormData = Omit<
  UserVisaResponse,
  'id' | 'user_id' | 'start_date' | 'end_date' | 'issue_date'
> & {
  start_date: Dayjs | null;
  end_date: Dayjs | null;
  issue_date: Dayjs | null;
};

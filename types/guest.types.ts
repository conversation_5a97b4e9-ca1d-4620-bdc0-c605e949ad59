import { Dayjs } from 'dayjs';

export type GuestResponse = {
  id: number;
  gender: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_code: string;
  phone_number: string;
  birth_date: string;
  nationality: string;
  identity_number?: string;
  passport_number?: string;
  passport_valid_date?: string;
  loyalty_airline?: string;
  loyalty_card_number?: string;
};

export type GuestCreateRequest = Omit<GuestResponse, 'id'>;

export type GuestUpdateRequest = GuestCreateRequest;

export type GuestFormData = Omit<GuestResponse, 'id' | 'birth_date' | 'passport_valid_date'> & {
  birth_date: Dayjs | null;
  passport_valid_date?: Dayjs | null;
};

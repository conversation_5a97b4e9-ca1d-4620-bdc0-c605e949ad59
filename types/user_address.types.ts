export type UserAddressResponse = {
  id: number;
  user_id: number;
  type: 'home' | 'work' | 'other';
  address_line_1: string;
  address_line_2?: string;
  country_code: string;
  city?: string;
  neighborhood_street?: string;
  postal_code?: string;
  is_default: boolean;
};

export type UserAddressCreateRequest = Omit<UserAddressResponse, 'id' | 'user_id'>;

export type UserAddressUpdateRequest = UserAddressCreateRequest;

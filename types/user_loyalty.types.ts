import dayjs from 'dayjs';

export type UserLoyaltyResponse = {
  id: number;
  user_id: number;
  airline_id: number;
  card_number: string;
  start_date: string;
  end_date: string;
  is_default: boolean;
  airline?: {
    id: number;
    airline_code: string;
    airline_name: string;
  };
};

export type UserLoyaltyCreateRequest = Omit<UserLoyaltyResponse, 'id' | 'user_id' | 'airline'>;

export type UserLoyaltyUpdateRequest = UserLoyaltyCreateRequest;

export type UserLoyaltyFormData = Omit<
  UserLoyaltyResponse,
  'id' | 'user_id' | 'start_date' | 'end_date' | 'airline'
> & {
  start_date: dayjs.Dayjs | null;
  end_date: dayjs.Dayjs | null;
};

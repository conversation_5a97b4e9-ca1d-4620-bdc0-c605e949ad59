import { Dayjs } from 'dayjs';

export type UserPassportResponse = {
  id: number;
  user_id: number;
  nationality: string;
  passport_number: string;
  issue_date: string;
  expiry_date: string;
  is_default: boolean;
};

export type UserPassportCreateRequest = Omit<UserPassportResponse, 'id' | 'user_id'>;

export type UserPassportUpdateRequest = UserPassportCreateRequest;

export type UserPassportFormData = Omit<
  UserPassportResponse,
  'id' | 'user_id' | 'issue_date' | 'expiry_date'
> & {
  issue_date: Dayjs | null;
  expiry_date: Dayjs | null;
};

#### FORMLAR
Uygulamada formlar Ant Design (Antd) Form bileşeni ile oluşturulmakta olup, form gönderiminde Next.js Server Action mimarisi kullanılmaktadır. onFinish metodu içerisinde form verileri FormData nesnesi ile sarmalanarak ilgili server action’a iletilir. Server Action içerisinde formData.get() yöntemiyle veriler ayrıştırılır ve kök dizinde konumlanan api/ klasörü altındaki ilgili servis metoduna HTTP isteği gerçekleştirilir.

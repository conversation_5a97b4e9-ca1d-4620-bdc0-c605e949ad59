{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.6", "antd": "^5.24.9", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "jose": "^6.0.11", "lucide-react": "^0.488.0", "next": "^15.2.4", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "sonner": "^2.0.5", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/nprogress": "^0.2.3", "@types/prop-types": "^15.7.15", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "typescript": "^5"}}